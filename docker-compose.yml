# NOTE: YAML inheritance is NOT recursive, so inheriting from a top-level service definition and then
#       re-defining a top-level attribute _overrides it completely_ rather than merging in the new values.
#
#       It also works with hashes only (not arrays).
#
# NOTE: Default ENV Var values are set in .env, but can be overridden on the command line or by ENV vars in
#       in the shell session.
services:

  # Abstract services - not intended to be invoked directly, but inherited from by other services.
  # These must be defined first in order for the YAML anchors to work.
  ############################################################################################################

  # Abstract database service - not actually intended to be run, but inherited from
  abstract-db: &abstract_db
    image: mysql/mysql-server:8.0
    command: ['mysqld', '--character-set-server=utf8', '--collation-server=utf8_general_ci']
    healthcheck:
       test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
       interval: 10s
       timeout: 20s
       retries: 10

  # Abstract service all other Rails development services inherit from
  abstract-env: &abstract_env
    image: ${DOCKER_IMAGE}
    # We need tty to get the Rails server's logs (which are being output to StdOut)
    tty: true
    # Mount the current directory as the app's source files (overriding/hiding the container source files)
    # and mount volumes where we expect a lot of I/O to occur that we don't want saved to the host filesystem
    volumes:
      - .:/home/<USER>/webapp:cached
      - rails_cache:/home/<USER>/webapp/tmp/cache
      - /run/host-services/ssh-auth.sock:/ssh-auth.sock

    environment: &abstract_env_vars
      BOOTSNAP_CACHE_DIR: /home/<USER>/webapp/.bundle/_bootsnap
      DATABASE_PASSWORD: ${DATABASE_PASSWORD}
      DATABASE_USERNAME: ${DATABASE_USERNAME}
      DATABASE_NAME: ${DATABASE_NAME}
      WEB_CONCURRENCY: ${WEB_CONCURRENCY:-1}
      MALLOC_ARENA_MAX: 2
      HISTFILE: /home/<USER>/webapp/log/.bash_history
      SSH_AUTH_SOCK: /ssh-auth.sock
    links:
      - db-server
      - redis-server

  # Concrete services - invoke with `docker-compose up <service-name>` for background or daemon services and
  # `docker-compose run <service-name>` for services you wish to interact with
  ############################################################################################################

  # Database

  # Development database server. Usually you will just start this service as a dependency of one of the other
  # ones.
  db-server: &dev_db
    <<: *abstract_db
    environment:
      MYSQL_PASSWORD: ${DATABASE_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DATABASE_PASSWORD}
      MYSQL_USER: ${DATABASE_USERNAME}
      MYSQL_DATABASE: ${DATABASE_NAME}
    volumes:
      - dev_db_logs:/var/log
      - dev_db:/var/lib/mysql

  # Development database client (this is the one you want to run any database commands or import a database dump)
  # Run as `docker-compose run db-console`
  db-console:
    <<: *dev_db
    # We need StdIn to be connected in order to pipe in database dumps
    tty: true
    # We need to use sh -c here to get the environment variables to interpolate in the container's shell
    # rather than in the docker-compose file. (i.e. We want the values defined in the docker-compose file and
    # NOT values defined as env vars when calling docker-compose)
    command: ["sh", "-c", "mysql -u $${MYSQL_USER} -p$${MYSQL_PASSWORD} -h db-server $${MYSQL_DATABASE}"]
    links:
      - db-server

  # Redis

  redis-server:
    image: redis:6.2.14

  # Application

  # Starts the rails server and mounts on port 3000
  app: &app
    <<: *abstract_env
    command: bundle exec rails s -p 3000 -b 0.0.0.0
    container_name: uown_api
    environment:
      <<: *abstract_env_vars
      DATABASE_HOST: db-server
      REDIS_URL: redis://redis-server:6379
      # We log to StdOut rather than filling up the container with log files
      RAILS_LOG_TO_STDOUT: 'true'
    ports:
      - 3000:3000
    depends_on:
      db-server:
        condition: service_healthy

  # Starts the application without mounting any volumes to reveal how the application will perform in
  # production with just the behaviour encoded by the Dockerfile + config to point at the development database
  app-without-dev-volumes:
    <<: *app
    volumes: []

  # Consoles - use `docker-compose run <service-name>`

  # Opens a bash console suitable for running commands that affect the development environment
  console: &dev_env
    <<: *abstract_env
    command: /bin/bash
    environment:
      <<: *abstract_env_vars
      DATABASE_HOST: db-server

  # Opens a rails console for the development environment
  rails-console:
    <<: *dev_env
    command: bundle exec rails c

  # Tests

  # Test database server, for running the test suite against
  test-db-server: &test_db
    <<: *abstract_db
    environment:
      MYSQL_PASSWORD: ${DATABASE_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DATABASE_PASSWORD}
      MYSQL_USER: ${DATABASE_USERNAME}
      MYSQL_DATABASE: ${TEST_DATABASE_NAME}

  # Test database client
  test-db-console:
    <<: *test_db
    # We need StdIn to be connected in order to pipe in database dumps
    tty: true
    # We need to use sh -c here to get the environment variables to interpolate in the container's shell
    # rather than in the docker-compose file. (i.e. We want the values defined in the docker-compose file and
    # NOT values defined as env vars when calling docker-compose)
    command: [ "sh", "-c", "mysql -u $${MYSQL_USER} -p$${MYSQL_PASSWORD} -h test-db-server $${MYSQL_DATABASE}" ]
    links:
      - test-db-server

  # Opens a bash console suitable for running commands that affect the test environment
  abstract-test-env: &abstract_test_env
    <<: *abstract_env
    environment:
      <<: *abstract_env_vars
      RAILS_ENV: test
      DATABASE_PASSWORD: ${DATABASE_PASSWORD}
      DATABASE_USERNAME: ${DATABASE_USERNAME}
      DATABASE_NAME: ${TEST_DATABASE_NAME}
      DATABASE_HOST: test-db-server
    links:
      - test-db-server
    depends_on:
      test-db-server:
        condition: service_healthy

  test-console:
    <<: *abstract_test_env
    command: /bin/bash

  # Runs the application's test suite
  tests: &tests
    <<: *abstract_test_env
    command: bundle exec rspec

  # Runs the application's test suite without any volumes (required for bitbucket pipelines)
  tests-without-volumes:
    <<: *tests
    volumes: []

  # Runs only the specs that failed in the last testsuite execution
  tests-only-failures:
    <<: *abstract_test_env
    command: bundle exec rspec --only-failures

volumes:
  dev_db:
  dev_db_logs:
  rails_cache:
