namespace :uown do
  namespace :direct_debits do
    desc 'Schedule/request payments from mandates'
    task schedule: :environment do
      Mandate.due.find_each do |mandate|
        begin
          mandate.request_money
          Rails.logger.info "INFO: Processed mandate #{mandate.mangopay_mandate_id}"
        rescue => e
          if mandate.mangopay_mandate.status == 'FAILED'
            mandate.update_attribute(:cancelled_at, Time.now)
            PayinsDirectDebitDelivery.notify(:setup_failure, mandate.user)
          end

          Rails.logger.error "ERROR: Failed to request money for mandate #{mandate.id} - #{e.message}"
        end
      end
    end

    desc 'Check direct debits and email on success/failure'
    task process: :environment do
      begin
        PaymentLog.where(kind: 'DIRECT_DEBIT', successful_at: nil, failed_at: nil).find_each do |payment_log|
          payin = MangoPay::PayIn.fetch(payment_log.mangopay_id)
          status = payin['Status']
          mangopay_user_id = payin['CreditedUserId']
          user = User.find_by(mangopay_id: mangopay_user_id)

          case status
          when 'SUCCEEDED'
            PayinsDirectDebitDelivery.notify(:successful, user, payment_log)
            payment_log.successful!(status)
          when 'FAILED'
            PayinsDirectDebitDelivery.notify(:failure, user, payment_log)
            payment_log.failure!(status)
          when 'CREATED'
            Rails.logger.info "INFO: Still pending #{payment_log.mangopay_id} (#{status})"
          else
            Rails.logger.error "ERROR: Unknown STATUS for #{payment_log.mangopay_id} (#{status})"
          end
        end
      rescue => e
        Rails.logger.error "ERROR: Unknown  for #{e.message}"
      end
    end
  end
end
