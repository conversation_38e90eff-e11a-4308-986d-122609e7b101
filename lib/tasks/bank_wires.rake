namespace :uown do
  namespace :bank_wires do
    desc 'Check bank wires and email on success/failure'
    task process: :environment do
      PaymentLog.where(kind: 'BANK_WIRE', successful_at: nil, failed_at: nil).find_each do |payment_log|
        begin
          mangopay_class = if payment_log.direction == 'credit'
                             MangoPay::PayIn
                           else
                             MangoPay::PayOut
                           end

          mangopay_transaction = mangopay_class.fetch(payment_log.mangopay_id)
          status = mangopay_transaction['Status']

          case status
          when 'SUCCEEDED'
            if payment_log.direction == 'credit'
              # Reflect the actual amount they paid in
              payment_log.amount = mangopay_transaction['DebitedFunds']['Amount']
              PayinsBankWireDelivery.notify(:successful, payment_log.user, payment_log)
            else
              PayinsBankWireDelivery.notify(:successful, payment_log.user, payment_log)
            end

            payment_log.successful!(status)
          when 'FAILED'
            PayinsBankWireDelivery.notify(:failure, payment_log.user, payment_log) if payment_log.direction == 'debit'

            payment_log.failure!(status)
          when 'CREATED'
            Rails.logger.info "INFO: Still pending #{payment_log.id} (#{status})"
          else
            Rails.logger.error "ERROR: Unknown STATUS for #{payment_log.id} (#{status})"
          end
        rescue => error
          Rails.logger.error "ERROR: Failed to process bank wire - #{error.message}"
          Airbrake.notify(error)
        end
      end
    end
  end
end
