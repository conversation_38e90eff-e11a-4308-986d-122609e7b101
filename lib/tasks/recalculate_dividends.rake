namespace :uown do
  desc 'Recalculate dividend amounts'
  task recalculate_dividends: :environment do
    Property::Dividend.includes(:payout).each do |dividend|
      user = dividend.user
      grouped_dividend = dividend.payout.grouped_dividends.find { |gd| gd[:user] == user }

      puts "Updating Dividend: #{dividend.amount} / #{grouped_dividend[:amount]}"
      dividend.update_column(:amount, grouped_dividend[:amount])
    end
  end
end
