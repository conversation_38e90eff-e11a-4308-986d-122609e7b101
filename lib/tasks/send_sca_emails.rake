require 'tempfile'

namespace :uown do
  desc 'Send SCA email to all users'
  task :send_sca_emails, [:dry_run] => :environment do |_t, args|
    include Tasks::LoggerHelper
    users = User.where(sca_status: false)
    log_message("Ids of unverified  users #{users.ids}")

    unless dry_run(args[:dry_run])
      log_message("System is sending emails to all users.")
      users.each do |user|
        ScaMailer.enroll(user).deliver_later
      end
    end
  end
end
