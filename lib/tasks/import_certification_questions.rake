namespace :uown do
  desc 'Import initial certification questions'
  task import_certification_questions: :environment do
    questions = [
      { question: 'I understand that:',
        answers: [
          { answer: 'Property prices only go up.', correct: false },
          { answer: 'Property prices go up as well as down.', correct: true },
          { answer: 'Property prices never change.', correct: false }
        ] },
      { question: 'Can I get my money out of my investment immediately?',
        answers: [
          { answer: 'No: I have to list my shares for sale and wait for UOWN to find a buyer for my shares.', correct: true },
          { answer: 'No: I can never get my money out of my investment.', correct: false },
          { answer: 'Yes: I can list sell my shares on the stock market and get my money instantly.', correct: false }
        ] },
      { question: 'I understand that the forecasts and projections provided by UOWN:',
        answers: [
          { answer: 'Are only forecasts and real returns can be higher or lower.', correct: true },
          { answer: 'Are always correct and I will always receive the forecasted returns.', correct: false },
          { answer: 'Are legally binding and I must receive the amount stated.', correct: false }
        ] },
      { question: 'A good investment strategy is to:',
        answers: [
          { answer: 'Invest all my income into one investment.', correct: false },
          { answer: 'Take out loans to invest as much money as possible.', correct: false },
          { answer: 'Invest some disposable income into a diverse range of investments.', correct: true }
        ] }
    ]

    Certification::Question.destroy_all

    Certification::Level.find_each do |level|
      questions.each do |question|
        q = Certification::Question.create(question: question[:question], level: level)

        question[:answers].each do |answer|
          Certification::Answer.create(answer: answer[:answer], correct: answer[:correct], question: q)
        end
      end
    end
  end
end
