require 'paperclip/transition_to_active_storage'

namespace :uown do
  desc 'Import paperclip attachments into active storage'
  task import_paperclip_attachments: :environment do
    Paperclip::TransitionToActiveStorage.new(Certification::Level, :attachment).execute
    Paperclip::TransitionToActiveStorage.new(InvestmentDocument, :attachment).execute
    Paperclip::TransitionToActiveStorage.new(Property::Document, :attachment).execute
    Paperclip::TransitionToActiveStorage.new(Property::Floorplan, :attachment).execute
    Paperclip::TransitionToActiveStorage.new(Property::LegalDocument, :attachment).execute
    Paperclip::TransitionToActiveStorage.new(Property::Loan, :acknowledgement_pdf).execute
    Paperclip::TransitionToActiveStorage.new(Property::Photo, :attachment).execute
    Paperclip::TransitionToActiveStorage.new(Property::Tag, :attachment).execute
  end
end
