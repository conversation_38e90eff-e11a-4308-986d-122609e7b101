namespace :uown do
  desc 'Check inactive users'
  task :inactive_users, [:dry_run, :year_ago] => :environment do |_t, args|
    include Tasks::LoggerHelper
    users = User.registered.where('created_at < ?', year_ago(args[:year_ago]))
    log_message("Ids of inactive users #{users.ids}")
    log_message("Mangopay Ids of inactive users #{users.map(&:mangopay_id).compact}")

    unless dry_run(args[:dry_run])
      log_message("Action is being performed")
      destroy_all(users)
    end
  end
end
