namespace :uown do
  namespace :st_annes do
    desc 'Output CSV of payments'
    task csv_report: :environment do
      file = Rails.root.join("#{property.slug}-payout-report.csv")

      CSV.open(file, 'w') do |csv|
        csv << ['Email',
                'Share Count',
                'Easy Exit Payout',
                'Easy Exit Fees',
                'Purchase Fees']

        report.items.each do |item|
          csv << [
            item.user.email,
            item.share_count,
            as_currency(item.easy_exit_payout),
            as_currency(item.easy_exit_fees),
            as_currency(item.purchase_fees)
          ]
        end

        csv << [
          'TOTALS',
          report.share_count_total,
          as_currency(report.easy_exit_payout_total),
          as_currency(report.easy_exit_fee_total),
          as_currency(report.purchase_fees_total)
        ]
      end
    end

    desc 'Console output of payments'
    task print_report: :environment do
      puts "Payouts For: #{property.name}"
      puts "Share Price: #{as_currency(property.share_price, 4)}"

      puts '--------------------------'
      puts 'Breakdown'
      puts '--------------------------'

      report.items.each do |item|
        puts "User: #{item.user.email}"
        puts "Shares: #{item.share_count}"
        puts "Easy Exit Total: #{as_currency(item.easy_exit_payout)}"
        puts "Sell Fees: #{as_currency(item.easy_exit_fees)}"
        puts "Buy Fees: #{as_currency(item.purchase_fees)}"
        puts '--------------------------'
      end

      puts 'Totals'
      puts '--------------------------'

      puts "Total Shares: #{report.share_count_total}"
      puts "Easy Exit Amount: #{as_currency(report.easy_exit_payout_total)}"
      puts "Easy Exit Fees: #{as_currency(report.easy_exit_fee_total)}"
      puts "Buy Fees: #{as_currency(report.purchase_fees_total)}"

      puts '--------------------------'
    end

    desc 'Build easy exit orders'
    task build_easy_exit_orders: :environment do
      puts 'Building easy exit orders'
      report.items.each do |item|
        item.new_easy_exit_order.save
        print '.'
      end
    end

    desc 'Bulk activate easy exit orders'
    task activate_easy_exit_orders: :environment do
      puts 'Activating easy exit orders'
      Share::EasyExitOrder.where(property: property).active.each(&:activate!)
    end

    desc 'Bulk reject easy exit orders'
    task reject_easy_exit_orders: :environment do
      puts 'Rejecting easy exit orders'
      Share::EasyExitOrder.where(property: property).active.each(&:reject!)
    end

    desc 'Build dividends'
    task build_dividends: :environment do
      puts 'Building dividends'
      payout_rent = Property::PayoutRent.new(user: Mango::LegalUser.root,
                                             property: property,
                                             start_date: '2019-03-31',
                                             end_date: '2019-04-01',
                                             amount: 0)

      report.items.each do |item|
        payout_rent.dividends.build(user: item.user, property: property, amount: item.easy_exit_fees) if item.easy_exit_fees.positive?
        payout_rent.dividends.build(user: item.user, property: property, amount: item.purchase_fees) if item.purchase_fees.positive?
      end

      payout_rent.save!
    end

    desc 'Hide Property'
    task hide_property: :environment do
      property.update_columns(visible: false)
    end

    desc 'Cancel Sell Order'
    task cancel_sell_order: :environment do
      property.sell_orders
              .active
              .where(user: Mango::LegalUser.root)
              .each(&:queue_cancellation!)
    end
  end
end

def report
  Payout::Summary.new(property: property)
end

def property
  @property ||= Property.find_by!(slug: 'refund-property-2')
end

# Display Helpers

def as_currency(amount, decimal_places = 2)
  "£#{(amount.to_d / 100).round(decimal_places)}"
end
