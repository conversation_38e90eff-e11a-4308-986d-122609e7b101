namespace :uown do
  desc 'Create mangopay legal user'
  task create_legal_user: :environment do
    params = { Tag: 'uOwn Admin',
               HeadquartersAddress: {
                 AddressLine1: '25-27 Otley Road',
                 AddressLine2: '',
                 City: 'Leeds',
                 Region: 'West Yorkshire',
                 PostalCode: 'LS6 3AA',
                 Country: 'GB'
               },
               LegalPersonType: 'BUSINESS',
               Name: '<PERSON><PERSON>',
               LegalRepresentativeAddress: {
                 AddressLine1: '25-27 Otley Road',
                 AddressLine2: '',
                 City: 'Leeds',
                 Region: 'West Yorkshire',
                 PostalCode: 'LS6 3AA',
                 Country: 'GB'
               },
               LegalRepresentativeBirthday: Date.parse('1993-01-02').to_datetime.to_i,
               LegalRepresentativeCountryOfResidence: 'GB',
               LegalRepresentativeNationality: 'GB',
               LegalRepresentativeEmail: '<EMAIL>',
               LegalRepresentativeFirstName: 'Haaris',
               LegalRepresentativeLastName: '<PERSON>',
               Email: '<EMAIL>' }

    begin
      legal_user = MangoPay::LegalUser.create(params)

      puts "Created MangoPay::LegalUser. ID is #{legal_user['Id']}"
    rescue MangoPay::ResponseError => e
      puts "Failed: #{e.details.inspect}"
    end
  end

  desc 'Submit KYC Docs for legal user'
  task kyc_legal_user: :environment do
    legal_user = Mango::LegalUser.root
    image = File.open(File.join(Rails.root, 'spec', 'fixtures', 'files', 'dubit_logo.png'))
    base64_image = Base64.strict_encode64(image.read)

    %w[IDENTITY_PROOF ARTICLES_OF_ASSOCIATION REGISTRATION_PROOF SHAREHOLDER_DECLARATION].each do |kind|
      kyc_document = MangoPay::KycDocument.create(legal_user.mangopay_id, Type: kind)

      MangoPay::KycDocument.create_page(legal_user.mangopay_id, kyc_document['Id'], base64_image)
      MangoPay::KycDocument.update(legal_user.mangopay_id, kyc_document['Id'], Status: 'VALIDATION_ASKED')
    end
  end
end
