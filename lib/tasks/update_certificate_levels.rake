namespace :uown do
  desc 'Update Certification::Levels with new details from Webflow.'
  task update_certificate_levels: :environment do
    Certification::Level.find_each do |certificate|
      case certificate.name
      when /(everyday investor)/
        puts "Updating Certificate Level #{certificate.name} [#{certificate.id}]"
        file = File.open("#{Rails.root}/app/assets/images/seeds/everyday_investor.png")

        certificate.update!(
          name: 'everyday investor',
          attachment: file,
          brief: "<li class='checklist-item'>I have some money set-aside to invest.</li>\r\n"\
                 "<li class='checklist-item'>I understand the risks and reward of investing.</li>"
        )

        file.close
      when /(sophisticated investor)/
        puts "Updating Certificate Level #{certificate.name} [#{certificate.id}]"
        file = File.open("#{Rails.root}/app/assets/images/seeds/sophisticated_investor.png")

        certificate.update!(
          name: 'sophisticated investor',
          attachment: file,
          brief: "<li class='checklist-item'>I have made one or more investments into an unlisted company, or</li>\r\n"\
                 "<li class='checklist-item'>I have professional investment experience or,</li>\r\n"\
                 "<li class='checklist-item'>I am the director of a successful business.</li>"
        )

        file.close
      when /(high net worth investor)/
        puts "Updating Certificate Level #{certificate.name} [#{certificate.id}]"
        file = File.open("#{Rails.root}/app/assets/images/seeds/high_net_worth_investor.png")

        certificate.update!(
          name: 'high net worth investor',
          attachment: file,
          brief: "<li class='checklist-item'>I have a total income of £100,000, or</li>\r\n"\
                 "<li class='checklist-item'>I have net assets of £250,000</li>"
        )

        file.close
      end
    end
  end
end
