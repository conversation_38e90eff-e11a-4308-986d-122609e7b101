require 'tempfile'

# How to run rake task
# rake uown:sync_to_flg_crm[value]
namespace :uown do
  desc 'Sync users to FlgCrm'
  task :sync_to_flg_crm, [:all] => :environment do |t, args|
    users = args[:all] == 'all' ? User.find_each : User.limit(5)
    users.each  do |u|
      resp = ::FlgCrm::Users.new(u).sync_to_crm
      puts "user id #{u.id}"
      puts "user flg id #{u.flg_lead_id}"
      puts "response status #{resp.success?}"
      puts "response error #{resp.errors}"
      puts '----------------------------'
    end

    rescue => e
      Airbrake.notify(e)
  end

  desc 'Update flg_lead_id for users'
  task update_flg_lead_id: :environment do
    begin
      # Define the path to your CSV file, keeping csv in public folder for now
      csv_file_path = Rails.root.join('public', 'csv', Rails.application.secrets.flg_crm[:csv_file_name])
      #TODO: how to read the dump file (csv) from the server (e.g. AWS S3 bucket)
      # Read the CSV file
      CSV.foreach(csv_file_path, headers: true) do |row|
        email = row['Email']
        field_to_update = row['Reference']
        # Find the user by email
        user = User.find_by(email: email)
        if user && user.flg_lead_id.blank?
          # Update the user with the field from the CSV
          user.update(flg_lead_id: field_to_update)
        end
      end
    rescue => e
      Airbrake.notify(e)
    end
  end
end
