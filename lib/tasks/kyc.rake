namespace :uown do
  namespace :kyc do
    desc 'Check kyc status'
    task check_status: :environment do
      # Find all users in the correct state ordered by oldest (most likely to be complete) first
      User.kyc_regular_is_pending.order(updated_at: :asc).find_each do |user|
        log_message("Checking KYC status for #{user.full_name}")

        if user.refresh_kyc_status!
          log_message('Complete')
        else
          log_message('Failed or One or more documents still requires review')
        end
      end
    end
  end
end

def log_message(message)
  puts message
  Rails.logger.warn(message)
end
