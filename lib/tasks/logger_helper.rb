# lib/tasks/logger_helper.rb
module Tasks
  module <PERSON><PERSON><PERSON><PERSON><PERSON>
    def log_message(message)
      puts message
      Rails.logger.warn(message)
    end

    def dry_run(value)
      value.blank? ? true : ActiveModel::Type::Boolean.new.cast(value)
    end

    def year_ago(year)
      (year.blank? ? 1 : year.to_i).year.ago
    end

    def destroy_all(users)
      return if users.blank?

      users.destroy_all
    end
  end
end