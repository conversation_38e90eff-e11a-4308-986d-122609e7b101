namespace :uown do
  desc 'Check spam users'
  task :spam_users, [:dry_run] => :environment do |_t, args|
    include Tasks::LoggerHelper
    users = User.where('email LIKE ?', "%@top-2%")
    log_message("Ids of spam users #{users.ids}")
    log_message("Mangopay Ids of spam users #{users.map(&:mangopay_id).compact}")

    unless dry_run(args[:dry_run])
      log_message("Action is being performed")
      users.each do |user|
        u = user.dup
        resp = if user.flg_lead_id.present?
                 FlgCrm::DestroyUserService.new(flg_lead_id: user.flg_lead_id).call
               else
                 '0'
               end
        if resp == '0'
          user.update_column(:flg_lead_id, nil)
          log_message("User with email(#{user.email}) has been deleted successfully from FLG CRM.")
          if user.destroy
            log_message("User with email(#{u.email}) has been deleted successfully from database.")
          else
            log_message("User with email(#{u.email}) failed to delete from database.")
          end
        else
          log_message("#{resp} : Faild to delete user from FLG CRM.")
          log_message("#{resp} : Faild to delete user from Database.")
        end
      end
    end
  end
end
