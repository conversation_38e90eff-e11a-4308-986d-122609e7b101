namespace :uown do
  desc 'Check zero wallet users'
  task :zero_wallet_users, [:dry_run, :year_ago] => :environment do |_t, args|
    include Tasks::LoggerHelper
    users = User.joins(:share_logs)
                 .where('share_logs.created_at < ?', year_ago(args[:year_ago]))
                 .where.not(users: { mangopay_id: nil })

    zero_wallet_users = users.select { |user| user.wallet.balance == 0 }

    log_message("Ids of 0 wallet users #{zero_wallet_users.ids}")
    log_message("Mangopay Ids of 0 wallet users #{zero_wallet_users.map(&:mangopay_id).compact}")

    unless dry_run(args[:dry_run])
      log_message("Action is being performed")
      destroy_all(zero_wallet_users)
    end
  end
end
