module Payout
  class Summary
    include ActiveModel::Model

    attr_accessor :property

    def items
      reports = []

      user_ids = Share::Log.where(property_id: property.id)
                           .where.not(user: Mango::LegalUser.root)
                           .pluck(:user_id)
                           .uniq

      user_ids.each do |user_id|
        user = User.find(user_id)
        report = Payout::Report.new(user: user, property: property)

        reports << report unless report.share_count.zero?
      end

      reports
    end

    # Totals

    def easy_exit_payout_total
      items.sum(&:easy_exit_payout)
    end

    def easy_exit_fee_total
      items.sum(&:easy_exit_fees)
    end

    def purchase_fees_total
      items.sum(&:purchase_fees)
    end

    def share_count_total
      items.sum(&:share_count)
    end
  end
end
