module Payout
  class Report
    include ActiveModel::Model

    attr_accessor :user, :property

    def share_count
      user.share_logs.where(property: property).sum(:quantity)
    end

    # Init Records

    def new_easy_exit_order
      Share::EasyExitOrder.new(user: user, property: property, quantity: share_count)
    end

    # Fees

    def easy_exit_fees
      new_easy_exit_order.calculated_fees
    end

    def easy_exit_payout
      new_easy_exit_order.total_with_fees
    end

    # NOTE: This is assuming share price has never changed (which it hasnt)
    def purchase_fees
      ((property.share_price * share_count) * Share::BuyOrder::FEE_PERCENTAGE).ceil
    end
  end
end
