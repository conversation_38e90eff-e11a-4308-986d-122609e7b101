class Marketing::Users
  class << self
    def push_to_email_octopus
      # Ensure all the list fields exist
      Marketing::List.create_list_fields

      records.each do |user|
        begin
          Marketing::List.upsert_user(user)
        rescue => error
          Airbrake.notify(error, { user_id: user.id })

          Rails.logger.warn("Failed to create/update user #{user.id}: #{error.message}")
        end

        sleep(0.2) # FIXME: Unfortunately the email octopus api has a 5 requests per second rate limit.
      end
    end

    def records
      User.where(marketing: true)
    end
  end
end
