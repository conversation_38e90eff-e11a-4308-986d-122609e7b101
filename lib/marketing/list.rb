class Marketing::List
  LIST_ID = Rails.application.secrets.email_octopus[:list_id]

  HEADINGS = {
    'uownId' => 'NUMBER',
    'title' => 'TEXT',
    'dateOfBirth' => 'TEXT',
    'countryOfResidence' => 'TEXT',
    'nationality' => 'TEXT',
    'experience' => 'TEXT',
    'plannedInvestment' => 'TEXT',
    'kind' => 'TEXT',
    'certification' => 'TEXT',
    'lastSigninAt' => 'TEXT',
    'createdAt' => 'TEXT',
    'state' => 'TEXT',
    'allInvestments' => 'TEXT',
    'currentInvestments' => 'TEXT'
  }.freeze

  class << self
    def list
      EmailOctopus::List.fetch(id: LIST_ID)
    end

    def create_list_fields
      list_fields = list.fields

      HEADINGS.each do |tag, type|
        next if list_fields.collect { |list_field| list_field.tag == tag }.any?

        EmailOctopus::ListField.create({ list_id: LIST_ID }, { tag: tag, type: type, label: tag.titleize })
      end
    end

    def upsert_user(user)
      formatted_email = user.email.downcase.strip
      email_md5 = Digest::MD5.hexdigest(formatted_email)

      params = {
        email_address: formatted_email,
        fields: {
          first_name: user.first_name,
          last_name: user.last_name,
          uownId: user.id,
          title: user.title,
          dateOfBirth: user.date_of_birth,
          countryOfResidence: user.country_of_residence,
          nationality: user.nationality,
          experience: user.experience,
          plannedInvestment: user.planned_investment,
          kind: user.kind,
          certification: user.certification,
          lastSigninAt: user.last_sign_in_at,
          createdAt: user.created_at,
          state: user.aasm_state,
          allInvestments: investments_for_user(user).keys.sort.join(';'),
          currentInvestments: investments_for_user(user).delete_if { |k,v| v.zero? }.keys.sort.join(';')
        }
      }

      # TODO: Switch to using bulk update API for all existing contacts
      begin
        if user.email_octopus_id.present?
          update_existing_email_octopus_contact(user, params)
        else
          create_new_email_octopus_contact(user, params)
        end
      rescue EmailOctopus::ResponseError => error
        case error.error
        when "MEMBER_NOT_FOUND"
          create_new_email_octopus_contact(user, params)
        when "MEMBER_EXISTS_WITH_EMAIL_ADDRESS"
          existing_contact = EmailOctopus::Contact.fetch({ list_id: LIST_ID, id: email_md5 })

          user.update(email_octopus_id: existing_contact.id)

          update_existing_email_octopus_contact(user, params)
        else
          raise error
        end
      end
    end

    def create_new_email_octopus_contact(user, params)
      new_email_octopus_contact = EmailOctopus::Contact.create({ list_id: LIST_ID }, params)

      user.update(email_octopus_id: new_email_octopus_contact.id)
    end

    def update_existing_email_octopus_contact(user, params)
      EmailOctopus::Contact.update({ list_id: LIST_ID, id: user.email_octopus_id }, params)
    end

    def investments_for_user(user)
      ::Share::Log.select('sum(share_logs.quantity) as total,
                           max(share_logs.id),
                           share_logs.property_id')
                  .includes(:property)
                  .where(user_id: user.id)
                  .group(:property_id)
                  .each_with_object({}) { |share_log, memo| memo[share_log.property_id.to_s.rjust(2, '0')] = share_log.total }
    end
  end
end
