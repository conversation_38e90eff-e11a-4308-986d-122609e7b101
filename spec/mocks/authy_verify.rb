RSpec.configure do |config|
  config.before do
    stub_request(:get, /https:\/\/api.authy.com\/protected\/json\/verify\/6393606\/(\d*)\?force=true/)
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/authy_valid_verify.json").read)

    stub_request(:get, /https:\/\/api.authy.com\/protected\/json\/verify\/6063936\/(\d*)\?force=true/)
      .to_return(status: 400, body: File.open("#{Rails.root}/spec/fixtures/authy_invalid_verify.json").read)
  end
end
