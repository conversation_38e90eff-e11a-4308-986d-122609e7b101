RSpec.configure do |config|
  config.before do
    stub_request(:post, 'https://api.authy.com/protected/json/users/new')
      .with(body: { send_install_link_via_sms: 'true',
                    user: { email: /user-(\d*)@dubitlimited.com/,
                            cellphone: '07834123456',
                            country_code: '44' } })
      .to_return(status: 200, body: File.open("#{Rails.root}/spec/fixtures/authy_valid_user.json").read)

    stub_request(:post, 'https://api.authy.com/protected/json/users/new')
      .with(body: { send_install_link_via_sms: 'true',
                    user: { email: /user-(\d*)@dubitlimited.com/,
                            cellphone: '1234',
                            country_code: '44' } })
      .to_return(status: 400, body: File.open("#{Rails.root}/spec/fixtures/authy_invalid_user.json").read)
  end
end
