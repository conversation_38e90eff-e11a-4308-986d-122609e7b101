RSpec.configure do |config|
  config.before do
    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/test/mandates/directdebit/web')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_direct_debit.json").read)
  end

  config.before do
    stub_request(:post, 'https://api.sandbox.mangopay.com/v2.01/test/payins/directdebit/direct')
      .to_return(status: 200, body: File.open("#{UownCore::Engine.root}/spec/fixtures/mangopay_direct_debit.json").read)
  end
end
