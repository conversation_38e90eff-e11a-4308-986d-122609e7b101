require 'rails_helper'

RSpec.describe User, type: :model do
  let(:user) { create(:user_natural) }

  let(:authy_number) { '07834123456' }
  let(:authy_id) { 12_345 }

  let(:two_factor_user) { create(:user_natural, authy_number: authy_number, authy_id: authy_id) }

  before do
    create_list(:user_natural, 10)
  end

  describe '.to_comma' do
    let(:fields) do
      %w[id title first_name middle_name last_name email
         date_of_birth address_1 address_2 city region
         post_code country phone_number country_of_residence
         nationality occupation call_me confirmed_at
         sign_in_count last_sign_in_at created_at updated_at
         state]
    end

    it 'exports without error and includes all fields' do
      output = User.all.to_comma

      fields.each do |field|
        expect(output).to include(field.humanize)
      end
    end
  end

  describe '.search' do
    before do
      user.update(first_name: '<PERSON>',
                  last_name: 'Bloggs',
                  email: '<EMAIL>')
    end

    let(:email_term) { user.email.split('@').first }
    let(:name_term) { user.full_name }

    it 'filters by email' do
      expect(User.count).not_to eq(1)
      expect(User.search(email_term).count).to eq(1)
    end

    it 'filters by name' do
      expect(User.count).not_to eq(1)
      expect(User.search(name_term).count).to eq(1)
    end

    it 'filters by state' do
      create(:user_natural, :confirmed, :kyc_light_complete)

      expect(User.search(nil, 'kyc_light_is_complete').count).to eq(User.kyc_light_is_complete.count)
    end

    it 'filters by referal host' do
      referer_host = 'google.com'

      create(:user_natural, referer_host: referer_host)

      expect(User.search(nil, nil, referer_host).count).to eq(User.where(referer_host: referer_host).count)
    end
  end

  describe '#is_2fa_setup?' do
    context 'setup' do
      it 'returns true' do
        expect(two_factor_user.is_2fa_setup?).to eq(true)
      end
    end

    context 'not setup' do
      it 'returns false' do
        expect(user.is_2fa_setup?).to eq(false)
      end
    end
  end

  describe '#register_2fa(number)' do
    context 'valid' do
      it 'sets the correct fields' do
        expect(user.register_2fa('44', authy_number)).to eq(true)
        expect(user.authy_number).to eq(authy_number)
        expect(user.authy_id).to eq(authy_id)
      end
    end

    context 'invalid' do
      let(:authy_number) { '1234' }

      it 'adds an error to the user' do
        expect(user.register_2fa('44', authy_number)).to eq(false)
        expect(user.errors[:authy_number]).to eq(['must be a valid cellphone number.'])
      end
    end
  end

  describe '#request_2fa' do
    context 'setup' do
      it 'returns true' do
        expect(two_factor_user.request_2fa).to eq(true)
      end
    end

    context 'not setup' do
      it 'returns false' do
        expect(user.request_2fa).to eq(false)
      end
    end
  end

  describe '#verify_2fa(token)' do
    let(:valid_token) { '6393606' }
    let(:invalid_token) { '6063936' }

    context 'valid' do
      it 'returns true' do
        expect(two_factor_user.verify_2fa(valid_token)).to eq(true)
      end
    end

    context 'invalid' do
      it 'adds an error to the user' do
        expect(two_factor_user.verify_2fa(invalid_token)).to eq(false)
        expect(two_factor_user.errors[:authy_token]).to eq(['is invalid'])
      end
    end
  end
end
