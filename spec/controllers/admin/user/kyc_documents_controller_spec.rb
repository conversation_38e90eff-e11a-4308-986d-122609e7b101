require 'rails_helper'

RSpec.describe Admin::User::KycDocumentsController, type: :controller do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:bad_user) { create(:user_natural, :confirmed) }
  let(:user_legal) { create(:user_legal, :confirmed, :kyc_light_complete) }
  let(:kyc_document) { create(:user_mangopay_kyc_document, user: user) }

  before do
    login_user
  end

  describe 'index' do
    before do
      create_list(:user_mangopay_kyc_document, 2, user: user)
    end

    it 'renders correctly' do
      get :index, params: { user_id: user.id }
      expect(response.status).to eq(200)
    end
  end

  describe 'new' do
    it 'renders correctly for natural user' do
      get :new, params: { user_id: user.id }
      expect(response.status).to eq(200)
    end

    it 'renders correctly for legal user' do
      get :new, params: { user_id: user_legal.id }
      expect(response.status).to eq(200)
    end

    it 'errors for invalid user' do
      get :new, params: { user_id: bad_user.id }
      expect(response.status).to eq(302)
      expect(flash[:error]).to eq('User is not in correct state')
    end
  end

  describe 'create' do
    let(:valid_params) { attributes_for(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }
    let(:file) { fixture_file_upload('dubit_logo.png', 'image/png') }

    it 'works correctly with correct params' do
      post :create, params: { user_id: user.id, user: valid_params.merge(identity_proof: file) }

      expect(response.status).to eq(302)
      expect(assigns(:user).aasm_state).to eq('kyc_regular_is_pending')
      expect(response.status).to redirect_to(admin_user_kyc_documents_path(user))
    end

    it 'fails correctly on validation' do
      post :create, params: { user_id: user.id, user: { first_name: 'Boris' } }

      expect(response.status).to eq(200)
      expect(response.status).to render_template(:new)
    end
  end

  describe 'refresh' do
    let(:user) { create(:user_natural, :confirmed, :kyc_light_complete, :kyc_regular_submitted) }

    it 'returns without error' do
      put :refresh, params: { user_id: user.id }
      expect(response.status).to eq(302)
      expect(flash[:error]).to eq('KYC Failed or One or more documents still requires review')
    end
  end
end
