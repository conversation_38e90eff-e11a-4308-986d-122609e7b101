require 'rails_helper'

RSpec.describe Admin::User::LoginAttemptsController, type: :controller do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }

  before do
    login_user
  end

  describe 'index' do
    context 'with user' do
      before do
        create_list(:user_login_attempt, 5, user: user)
      end

      it 'renders correctly' do
        get :index, params: { user_id: user.id }
        expect(response.status).to eq(200)
      end
    end

    context 'without user' do
      before do
        create_list(:user_login_attempt, 5)
      end

      it 'renders correctly' do
        get :index
        expect(response.status).to eq(200)
      end
    end
  end
end
