require 'rails_helper'

RSpec.describe Admin::User::QueuedActionsController, type: :controller do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }

  before do
    login_user
  end

  describe 'index' do
    context 'with user' do
      before do
        create(:user_queued_action_investment, user: user)
        create(:user_queued_action_payout, user: user)
      end

      it 'renders html' do
        get :index, params: { user_id: user.id }
        expect(response.status).to eq(200)
      end
    end

    context 'without user' do
      before do
        create_list(:user_queued_action_investment, 2)
        create_list(:user_queued_action_payout, 2)
      end

      it 'renders html' do
        get :index
        expect(response.status).to eq(200)
      end
    end
  end
end
