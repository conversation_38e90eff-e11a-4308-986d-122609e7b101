require 'rails_helper'

RSpec.describe Admin::User::CertificationAttemptsController, type: :controller do
  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }

  before do
    login_user
  end

  describe 'index' do
    context 'with user' do
      before do
        create_list(:user_certification_attempt, 5, user: user)
      end

      it 'renders html' do
        get :index, params: { user_id: user.id }
        expect(response.status).to eq(200)
      end

      it 'renders csv' do
        get :index, params: { user_id: user.id }, format: :csv
        expect(response.status).to eq(200)
      end
    end

    context 'without user' do
      before do
        create_list(:user_certification_attempt, 5)
      end

      it 'renders html' do
        get :index
        expect(response.status).to eq(200)
      end

      it 'renders csv' do
        get :index, params: { user_id: user.id }, format: :csv
        expect(response.status).to eq(200)
      end
    end
  end
end
