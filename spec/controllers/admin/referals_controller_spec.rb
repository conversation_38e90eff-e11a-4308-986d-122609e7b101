require 'rails_helper'

RSpec.describe Admin::ReferalsController, type: :controller do
  let(:user_natural) { create(:user_natural) }
  let(:user_legal) { create(:user_legal) }

  before do
    create_list(:user_natural, 5, referer_host: 'google.com')
    create_list(:user_natural, 5, referer_host: 'facebook.com', created_at: 1.year.ago)
    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
      expect(assigns(:referrals).size).to eq(2)
    end

    it 'filters by date' do
      get :index, params: { start_date: 6.months.ago }

      expect(response.status).to eq(200)
      expect(assigns(:referrals).size).to eq(1)
    end
  end
end
