require 'rails_helper'

RSpec.describe Admin::CertificationLevelsController, type: :controller do
  let(:certification_level) { create(:certification_level, :with_questions) }
  let(:certification_level2) { create(:certification_level, :with_questions) }

  before do
    create_list(:certification_level, 10)
    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end
  end

  describe 'new' do
    it 'renders correctly' do
      get :new
      expect(response.status).to eq(200)
    end
  end

  describe 'edit' do
    it 'renders correctly' do
      get :edit, params: { id: certification_level.id }
      expect(response.status).to eq(200)
    end
  end

  describe 'reorder' do
    it 'updates positions' do
      put :reorder, params: { certification_level: { certification_level.id => { position: 1 },
                                                     certification_level2.id => { position: 2 } } }

      expect(response.status).to eq(302)
      expect(certification_level.reload.position).to eq(1)
      expect(certification_level2.reload.position).to eq(2)
    end
  end
end
