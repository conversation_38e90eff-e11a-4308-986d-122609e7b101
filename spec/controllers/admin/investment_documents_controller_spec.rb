require 'rails_helper'

RSpec.describe Admin::InvestmentDocumentsController, type: :controller do
  let(:investment_document) { create(:investment_document) }

  before do
    create_list(:investment_document, 10)
    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end
  end

  describe 'new' do
    it 'renders correctly' do
      get :new
      expect(response.status).to eq(200)
    end
  end

  describe 'edit' do
    it 'renders correctly' do
      get :edit, params: { id: investment_document.id }
      expect(response.status).to eq(200)
    end
  end
end
