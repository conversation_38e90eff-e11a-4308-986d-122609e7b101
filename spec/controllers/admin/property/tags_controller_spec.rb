require 'rails_helper'

RSpec.describe Admin::Property::TagsController, type: :controller do
  let(:tag) { create(:property_tag) }

  before do
    create_list(:property_tag, 10)
    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end
  end

  describe 'new' do
    it 'renders correctly' do
      get :new
      expect(response.status).to eq(200)
    end
  end

  describe 'edit' do
    it 'renders correctly' do
      get :edit, params: { id: tag.id }
      expect(response.status).to eq(200)
    end
  end
end
