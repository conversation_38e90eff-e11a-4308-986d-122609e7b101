require 'rails_helper'

RSpec.describe Admin::Property::NewsItemsController, type: :controller do
  let(:property) { create(:property_development, :with_photos) }
  let(:news_item) { create(:property_news_item, property: property) }

  before do
    create_list(:property_news_item, 5, property: property)
    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index, params: { property_id: property.id }
      expect(response.status).to eq(200)
    end
  end

  describe 'new' do
    it 'renders correctly' do
      get :new, params: { property_id: property.id }
      expect(response.status).to eq(200)
    end
  end

  describe 'create' do
    let(:news_item_params) { attributes_for(:property_news_item, property: property) }

    it 'creates without error' do
      post :create, params: { property_id: property.id, property_news_item: news_item_params }
      expect(response).to redirect_to(admin_property_news_items_path(property))
    end
  end

  describe 'edit' do
    it 'renders correctly' do
      get :edit, params: { property_id: property.id, id: news_item.id }
      expect(response.status).to eq(200)
    end
  end

  describe 'create' do
    let(:new_title) { 'Updated News Item' }

    it 'creates without error' do
      expect(news_item.title).not_to eq(new_title)

      patch :update, params: { property_id: property.id, id: news_item.id, property_news_item: { title: new_title } }

      expect(response).to redirect_to(admin_property_news_items_path(property))
      expect(news_item.reload.title).to eq(new_title)
    end
  end
end
