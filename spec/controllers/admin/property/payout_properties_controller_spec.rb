require 'rails_helper'

RSpec.describe Admin::Property::PayoutPropertiesController, type: :controller do
  let(:property_payout_property) { create(:property_payout_property, property: property, amount: 20) }

  before do
    login_user
  end

  describe 'index' do
    before do
      create_list(:property_payout_property, 10)
    end

    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end
  end

  describe 'new' do
    it 'renders correctly' do
      get :new
      expect(response.status).to eq(200)
    end
  end

  context 'when a regular property' do
    let(:property) { create(:property_regular) }

    describe 'confirm_totals' do
      it 'renders correctly' do
        get :confirm_totals, params: { id: property_payout_property.id }
        expect(response.status).to eq(200)
      end
    end

    describe 'confirm_dividends' do
      it 'renders correctly' do
        property_payout_property.allocate_funds!
        property_payout_property.transfer_fees!

        get :confirm_dividends, params: { id: property_payout_property.id }
        expect(response.status).to eq(200)
      end
    end

    describe 'show' do
      it 'renders correctly' do
        property_payout_property.allocate_funds!
        property_payout_property.transfer_fees!
        property_payout_property.distribute_dividends!

        get :show, params: { id: property_payout_property.id }
        expect(response.status).to eq(200)
      end
    end
  end

  context 'when a development' do
    let(:property) { create(:property_development) }

    describe 'confirm_totals' do
      it 'renders correctly' do
        get :confirm_totals, params: { id: property_payout_property.id }
        expect(response.status).to eq(200)
      end
    end

    describe 'confirm_dividends' do
      it 'renders correctly' do
        property_payout_property.allocate_funds!
        property_payout_property.transfer_fees!

        get :confirm_dividends, params: { id: property_payout_property.id }
        expect(response.status).to eq(200)
      end
    end

    describe 'show' do
      it 'renders correctly' do
        property_payout_property.allocate_funds!
        property_payout_property.transfer_fees!
        property_payout_property.distribute_dividends!

        get :show, params: { id: property_payout_property.id }
        expect(response.status).to eq(200)
      end
    end
  end
end
