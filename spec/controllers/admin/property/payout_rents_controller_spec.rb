require 'rails_helper'

RSpec.describe Admin::Property::PayoutRentsController, type: :controller do
  let(:property_payout_rent) { create(:property_payout_rent, :with_items, amount: 20) }

  before do
    login_user
  end

  describe 'index' do
    before do
      create_list(:property_payout_rent, 10)
    end

    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end
  end

  describe 'new' do
    it 'renders correctly' do
      get :new
      expect(response.status).to eq(200)
    end
  end

  describe 'confirm_totals' do
    it 'renders correctly' do
      get :confirm_totals, params: { id: property_payout_rent.id }
      expect(response.status).to eq(200)
    end
  end

  describe 'confirm_dividends' do
    it 'renders correctly' do
      property_payout_rent.allocate_funds!
      property_payout_rent.transfer_fees!

      get :confirm_dividends, params: { id: property_payout_rent.id }
      expect(response.status).to eq(200)
    end
  end

  describe 'show' do
    it 'renders correctly' do
      property_payout_rent.allocate_funds!
      property_payout_rent.transfer_fees!
      property_payout_rent.distribute_dividends!

      get :show, params: { id: property_payout_rent.id }
      expect(response.status).to eq(200)
    end
  end
end
