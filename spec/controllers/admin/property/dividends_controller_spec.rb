require 'rails_helper'

RSpec.describe Admin::Property::DividendsController, type: :controller do
  before do
    create_list(:property_dividend, 10, payout: create(:property_payout_rent))

    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end

    it 'exports csv' do
      get :index, params: { format: 'csv' }
      expect(response.status).to eq(200)
    end
  end
end
