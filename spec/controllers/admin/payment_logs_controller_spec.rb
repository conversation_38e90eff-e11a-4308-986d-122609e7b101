require 'rails_helper'

RSpec.describe Admin::PaymentLogsController, type: :controller do
  before do
    create_list(:payment_log, 10)
    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end

    it 'exports csv' do
      get :index, params: { format: 'csv' }
      expect(response.status).to eq(200)
    end
  end
end
