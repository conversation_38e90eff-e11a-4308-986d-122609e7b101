require 'rails_helper'

RSpec.describe Admin::PropertyLoansController, type: :controller do
  let(:property) { create(:property_loan, :with_photos, :with_floorplans, :with_documents, :with_tags) }

  before do
    create_list(:property_loan, 5)
    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end
  end

  describe 'new' do
    it 'renders correctly' do
      get :new
      expect(response.status).to eq(200)
    end

    it 'should render correctly when placeholder' do
      get :new, params: { placeholder: true }
      expect(response.status).to eq(200)
    end
  end

  describe 'edit' do
    it 'renders correctly' do
      get :edit, params: { id: property.id }
      expect(response.status).to eq(200)
    end
  end

  describe 'upgrade' do
    let(:placeholder) { create(:property_loan, :placeholder) }

    it 'upgrades successfully for a placeholder' do
      put :upgrade, params: { id: placeholder.id }

      expect(response.status).to redirect_to(admin_property_loans_path)
      expect(flash[:notice]).to eq('Successfully upgraded')
      expect(placeholder.reload.placeholder).to eq(false)
      expect(placeholder.reload.available_shares).to eq(placeholder.share_count)
    end

    it 'returns an error for non placeholders' do
      put :upgrade, params: { id: property.id }

      expect(response.status).to redirect_to(admin_property_loans_path)
      expect(flash[:error]).to eq('Not a placeholder property')
    end
  end
end
