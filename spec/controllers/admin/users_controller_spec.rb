require 'rails_helper'

RSpec.describe Admin::UsersController, type: :controller do
  let(:user_natural) { create(:user_natural) }
  let(:user_legal) { create(:user_legal) }

  before do
    create_list(:user_natural, 5)
    create_list(:user_legal, 5)
    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end

    it 'exports csv' do
      get :index, params: { export_csv: true }

      expect(response.status).to eq(200)
    end
  end

  describe 'edit' do
    context 'natural' do
      it 'renders correctly' do
        get :edit, params: { id: user_natural.id }
        expect(response.status).to eq(200)
      end
    end

    context 'legal' do
      it 'renders correctly' do
        get :edit, params: { id: user_legal.id }
        expect(response.status).to eq(200)
      end
    end
  end
end
