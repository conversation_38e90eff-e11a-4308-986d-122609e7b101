require 'rails_helper'

RSpec.describe Admin::Share::TransferOrdersController, type: :controller do
  before do
    create_list(:share_transfer_order, 10)
    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end
  end

  describe 'new' do
    it 'renders correctly' do
      get :new
      expect(response.status).to eq(200)
    end
  end

  describe 'create' do
    let(:quantity) { 10 }
    let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
    let(:source_user) { create(:user_natural, :confirmed, :kyc_light_complete) }
    let(:property) { create(:property_regular) }
    let!(:create_share_log) { create(:share_log, property: property, user: source_user, quantity: quantity) }
    let(:share_transfer_order_params) do
      { share_transfer_order: attributes_for(:share_transfer_order, property_id: property.id,
                                                                    user_id: user.id,
                                                                    source_user_id: source_user.id,
                                                                    quantity: quantity) }
    end

    it 'redirects and creates valid record' do
      post :create, params: share_transfer_order_params

      expect(response).to redirect_to(admin_share_transfer_orders_path)
    end

    it 'renders new form on errors' do
      share_transfer_order_params[:share_transfer_order][:quantity_confirmation] = -1

      post :create, params: share_transfer_order_params

      expect(response).to render_template(:new)
    end
  end
end
