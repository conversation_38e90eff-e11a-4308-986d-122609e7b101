require 'rails_helper'

RSpec.describe Admin::Share::EasyExitOrdersController, type: :controller do
  before do
    login_user
  end

  let(:user) { create(:user_natural, :confirmed, :kyc_light_complete) }
  let(:valid_property) { create(:property_regular) }
  let(:quantity) { 10 }
  let!(:share_easy_exit_order) do
    create(:share_log, user: user,
                       property: valid_property,
                       quantity: quantity)

    create(:share_easy_exit_order, user: user,
                                   property: valid_property,
                                   quantity: quantity)
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end
  end

  describe 'show' do
    it 'renders correctly' do
      get :show, params: { id: share_easy_exit_order.id }
      expect(response.status).to eq(200)
    end
  end

  describe 'accept' do
    it 'accepts without error' do
      put :accept, params: { id: share_easy_exit_order.id }

      expect(response.status).to eq(302)
      expect(flash[:notice]).to eq('Easy exit will be processed shortly')
    end
  end

  describe 'reject' do
    it 'rejects without error' do
      put :reject, params: { id: share_easy_exit_order.id }

      expect(response.status).to eq(302)
      expect(flash[:notice]).to eq('Easy exit will be rejected shortly')
    end
  end
end
