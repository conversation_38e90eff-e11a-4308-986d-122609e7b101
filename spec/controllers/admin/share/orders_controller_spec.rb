require 'rails_helper'

RSpec.describe Admin::Share::OrdersController, type: :controller do
  let(:user) { create(:user_natural) }
  let(:property) { create(:property_regular) }

  before do
    create_list(:share_buy_order, 10, user: user, property: property)
    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
    end

    it 'exports csv' do
      get :index, params: { format: 'csv' }
      expect(response.status).to eq(200)
    end

    it 'can be scoped by user' do
      get :index, params: { user_id: user.id }

      expect(assigns(:user)).to eq(user)
      expect(response.status).to eq(200)
    end

    it 'can be scoped by property' do
      get :index, params: { property_id: property.id }

      expect(assigns(:property)).to eq(property)
      expect(response.status).to eq(200)
    end
  end

  describe 'cancel' do
    let(:quantity) { 10 }
    let(:buy_shares) { create(:share_log, user: user, property: property, quantity: quantity) }
    let(:share_sell_order) { create(:share_sell_order, property: property, user: user, quantity: quantity) }

    before do
      buy_shares
      request.env['HTTP_REFERER'] = '/admin/share_logs'
    end

    it 'cancels without error' do
      delete :cancel, params: { id: share_sell_order.id }

      expect(response.status).to eq(302)
      expect(flash[:notice]).to eq('Order has been queued for cancellation')
    end
  end
end
