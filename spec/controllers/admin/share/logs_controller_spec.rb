require 'rails_helper'

RSpec.describe Admin::Share::LogsController, type: :controller do
  let(:user) { create(:user_natural) }
  let(:property) { create(:property_regular) }

  before do
    create_list(:share_log, 10, user: user, property: property)
    login_user
  end

  describe 'index' do
    it 'renders correctly' do
      get :index
      expect(response.status).to eq(200)
      expect(response.body).not_to include('Grouped By User')
      expect(response.body).not_to include('Grouped By Property')
    end

    it 'exports csv' do
      get :index, params: { format: 'csv' }
      expect(response.status).to eq(200)
    end

    it 'can be scoped by user' do
      get :index, params: { user_id: user.id }

      expect(response.status).to eq(200)
      expect(assigns(:user)).to eq(user)
      expect(response.body).to include('Grouped By Property')
      expect(response.body).not_to include('Grouped By User')
    end

    it 'can be scoped by property' do
      get :index, params: { property_id: property.id }

      expect(response.status).to eq(200)
      expect(assigns(:property)).to eq(property)
      expect(response.body).to include('Grouped By User')
      expect(response.body).not_to include('Grouped By Property')
    end
  end
end
