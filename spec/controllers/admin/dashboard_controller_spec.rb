# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Admin::DashboardController, type: :controller do
  describe 'index' do
    it 'redirects if not logged in' do
      get :index

      expect(response.status).to eq(302)
      expect(response).to redirect_to(root_path)
    end

    it 'renders correctly if logged in' do
      login_user

      get :index

      expect(response.status).to eq(200)
    end
  end

  describe 'monthly_report' do
    context 'when not logged in' do
      before { post :monthly_report }

      it 'redirects to the root path' do
        expect(response.status).to eq(302)
        expect(response).to redirect_to(root_path)
      end
    end

    context 'when logged in' do
      before do
        login_user
        allow(AdminNotificationMailer).to receive_message_chain(:monthly_report, :deliver_later).and_return(true)
        post :monthly_report
      end

      it 'sends the monthly report' do
        expect(AdminNotificationMailer).to have_received(:monthly_report)
      end

      it 'redirects to the root path' do
        expect(response.status).to eq(302)
        expect(response).to redirect_to(root_path)
      end

      it 'sets the flash notice' do
        expect(flash[:notice]).to eq('Monthly report has been sent successfully.')
      end
    end
  end
end
