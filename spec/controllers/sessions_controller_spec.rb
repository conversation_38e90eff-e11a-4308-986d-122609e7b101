require 'rails_helper'

RSpec.describe <PERSON><PERSON>ontroller, type: :controller do
  let(:password) { 'Qwerty123$%$&!' }
  let(:user) { create(:user_natural, password: password) }

  let(:authy_number) { '07834123456' }
  let(:authy_id) { 12_345 }

  let(:two_factor_user) { create(:user_natural, password: password, authy_number: authy_number, authy_id: authy_id) }

  before do
    user.add_role(:administrator)
    two_factor_user.add_role(:administrator)
    allow(controller).to receive(:verify_recaptcha?).and_return(true)
  end

  describe '#create' do
    context '2fa user' do
      let(:request) { post :create, params: { user: { email: two_factor_user.email, password: password } } }

      it 'redirects to verify path' do
        expect(request).to redirect_to(new_two_factor_auth_validate_path)
      end
    end

    context 'non 2fa user' do
      let(:request) { post :create, params: { user: { email: user.email, password: password } } }

      it 'redirects to setup path' do
        expect(request).to redirect_to(new_two_factor_auth_setup_path)
      end
    end
  end
end
