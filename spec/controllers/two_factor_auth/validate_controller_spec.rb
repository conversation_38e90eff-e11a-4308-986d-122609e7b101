require 'rails_helper'

RSpec.describe TwoFactorAuth::ValidateController, type: :controller do
  let(:user) { create(:user_natural) }

  let(:authy_number) { '07834123456' }
  let(:authy_id) { 12_345 }

  let(:two_factor_user) { create(:user_natural, authy_number: authy_number, authy_id: authy_id) }

  describe '#new' do
    let(:request) { get :new }

    context '2fa user' do
      before do
        login_user(two_factor_user)
      end

      it 'renders form' do
        expect(request).to render_template(:new)
      end
    end

    context 'non 2fa user' do
      before do
        login_user(user)
      end

      it 'redirects to setup path' do
        expect(request).to redirect_to(new_two_factor_auth_setup_path)
      end
    end
  end
end
