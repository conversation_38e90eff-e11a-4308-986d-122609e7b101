require 'rails_helper'

RSpec.describe Marketing::Users do
  let(:non_marketing) { create(:user_natural, marketing: false) }
  let(:marketing) { create(:user_with_email_octopus_contact, marketing: true) }

  before do
    non_marketing
    marketing
  end

  describe '.push_to_email_octopus' do
    let(:list) { build(:email_octopus_list, name: 'uown_test') }

    before do
      list

      Marketing::List::HEADINGS.each do |tag, type|
        build(:email_octopus_list_field, tag: tag, type: type, label: tag.titleize)
      end

      marketing
    end

    it 'runs without error' do
      expect(described_class.push_to_email_octopus).not_to eq(false)
    end
  end

  describe '.records' do
    it 'includes marketing users' do
      expect(described_class.records).to include(marketing)
    end

    it 'doesnt include non marketing users' do
      expect(described_class.records).not_to include(non_marketing)
    end
  end
end
