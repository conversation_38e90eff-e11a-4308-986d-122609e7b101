require 'rails_helper'

RSpec.describe Marketing::List do
  let(:list) { build(:email_octopus_list, name: 'uown_test') }

  describe '.list' do
    it 'sets the correct name' do
      expect(list.name).to eq(described_class.list.name)
    end
  end

  describe '.create_list_fields' do
    before do
      list

      described_class::HEADINGS.each do |tag, type|
        build(:email_octopus_list_field, tag: tag, type: type, label: tag.titleize)
      end
    end

    it 'returns without error' do
      expect(described_class.create_list_fields).not_to eq(nil)
    end
  end

  describe '.upsert_user' do
    let(:marketing_user) { create(:user_with_email_octopus_contact) }

    it 'when no email_octopus_id present' do
      marketing_user.email_octopus_id = nil

      expect(described_class.upsert_user(marketing_user)).not_to eq(nil)
    end

    it 'when no email_octopus_id present' do
      expect(described_class.upsert_user(marketing_user)).not_to eq(nil)
    end
  end
end
