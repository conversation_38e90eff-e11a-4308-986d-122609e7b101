require 'rails_helper'
require 'rake'

RSpec.describe 'sync_to_crm', type: :task do
  before :all do
    # Load the rake tasks
    Rake.application.rake_require 'tasks/sync_to_crm'
    Rake::Task.define_task(:environment)
  end

  before do
    # Create a temporary CSV file
    @csv_file_path = Rails.root.join('public', 'csv', Rails.application.secrets.flg_crm[:csv_file_name])
    FileUtils.mkdir_p(File.dirname(@csv_file_path))
    File.open(@csv_file_path, 'w') do |file|
      file.write("Email,Reference\<EMAIL>,123\<EMAIL>,456\n")
    end

    # Create the user records
    @user1 = create(:user_natural, email: '<EMAIL>', flg_lead_id: nil)
    @user2 = create(:user_natural, email: '<EMAIL>', flg_lead_id: nil)
    @user3 = create(:user_natural, email: '<EMAIL>', flg_lead_id: '789')
  end

  after do
    # Clean up the CSV file and User records
    File.delete(@csv_file_path) if File.exist?(@csv_file_path)
  end

  it 'updates the users with the field from the CSV' do
    Rake::Task['uown:update_flg_lead_id'].invoke
    @user1.reload
    expect(@user1.flg_lead_id).to eq('123')
  end

  it 'not update the users if flg_lead_id is already set' do
    Rake::Task['uown:update_flg_lead_id'].invoke
    @user3.reload
    expect(@user3.flg_lead_id).to eq('789')
  end
end
