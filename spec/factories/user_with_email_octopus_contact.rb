FactoryBot.define do
  factory :user_with_email_octopus_contact, parent: :user_natural do
    after(:create) do |user|
      fields = { first_name: user.first_name,
                 last_name: user.last_name,
                 uownId: user.id,
                 title: user.title,
                 dateOfBirth: user.date_of_birth.to_s,
                 countryOfResidence: user.country_of_residence,
                 nationality: user.nationality,
                 experience: user.experience,
                 plannedInvestment: user.planned_investment,
                 kind: user.kind,
                 certification: user.certification,
                 lastSigninAt: user.last_sign_in_at,
                 createdAt: user.created_at.to_s,
                 state: user.aasm_state,
                 allInvestments: '',
                 currentInvestments: ''
               }

      contact = build(:email_octopus_contact, email_address: user.email, fields: fields)

      user.email_octopus_id = contact.id
    end
  end
end
