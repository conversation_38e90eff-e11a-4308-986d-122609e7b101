FactoryBot.define do
  factory :email_octopus_list_field, class: EmailOctopus::ListField do
    skip_create

    initialize_with { new(attributes.stringify_keys) }

    tag { 'middleName' }
    type { 'STRING' }
    label { 'Middle Name' }
    fallback { 'Unknown' }

    after(:build) do |list_field|
      request = { tag: list_field.tag,
                  type: list_field.type,
                  label: list_field.label,
                  api_key: Rails.application.secrets.email_octopus[:api_key][:email_octopus_api_key] }

      response = { tag: list_field.tag,
                   type: list_field.type,
                   label: list_field.label,
                   fallback: list_field.fallback }



      list_id = Rails.application.secrets.email_octopus[:list_id]

      WebMock.stub_request(:post, "https://emailoctopus.com/api/1.5/lists/#{list_id}/fields")
             .with(body: request.to_json.to_s)
             .to_return(status: 200, body: response.to_json.to_s)
    end
  end
end
