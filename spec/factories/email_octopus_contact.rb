FactoryBot.define do
  factory :email_octopus_contact, class: EmailOctopus::Contact do
    skip_create

    initialize_with { new(attributes.stringify_keys) }

    id { SecureRandom.hex(4) }
    sequence(:email_address) { |n| "email#{n}@example.com" }
    fields { [] }

    after(:build) do |contact|
      body = { email_address: contact.email_address,
               fields: contact.fields,
               api_key: Rails.application.secrets.email_octopus[:api_key][:email_octopus_api_key] }

      response = { id: contact.id,
                   email_address: contact.email_address,
                   fields: contact.fields }

      list_id = Rails.application.secrets.email_octopus[:list_id]
      email_md5 = Digest::MD5.hexdigest(contact.email_address)

      WebMock.stub_request(:post, "https://emailoctopus.com/api/1.5/lists/xxx/contacts")
             .to_return(status: 200, body: response.to_json.to_s)

      WebMock.stub_request(:get, "https://emailoctopus.com/api/1.5/lists/#{list_id}/contacts/#{email_md5}")
             .to_return(status: 200, body: response.to_json.to_s)

      WebMock.stub_request(:put, "https://emailoctopus.com/api/1.5/lists/#{list_id}/contacts/#{contact.id}")
             .with(body: body.to_json.to_s)
             .to_return(status: 200, body: response.to_json.to_s)
    end
  end
end
