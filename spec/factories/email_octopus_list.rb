FactoryBot.define do
  factory :email_octopus_list, class: EmailOctopus::List do
    skip_create

    initialize_with { new(attributes.stringify_keys) }

    id { Rails.application.secrets.email_octopus[:list_id] }
    name { 'email_octopus_list' }
    double_opt_in { false }
    created_at { Time.zone.now }
    fields { [] }

    after(:build) do |list|
      response = { id: list.id,
                   name: list.name,
                   double_opt_in: list.double_opt_in,
                   created_at: list.created_at,
                   fields: list.fields,
                   counts: {
                     pending: 0,
                     subscribed: 0,
                     unsubscribed: 0
                   }
                 }

      WebMock.stub_request(:get, "https://emailoctopus.com/api/1.5/lists/#{list.id}")
             .to_return(status: 200, body: response.to_json.to_s)
    end
  end
end
