# This config file is required to pass environment variables from <PERSON><PERSON><PERSON> to <PERSON>.
# As otherwise <PERSON><PERSON><PERSON> does not pass any environment variables to child processes.
# Set Nginx config environment based on
# the values set in the .env file

env RAILS_ENV;
env DATABASE_NAME;
env DATABASE_USERNAME;
env DATABASE_PASSWORD;
env DATABASE_HOST;
env SECRET_KEY_BASE;
env ERROR_ENVIRONMENT;
env FRONTEND_URL;
env S3_BUCKET;
env S3_ACCESS_KEY;
env S3_SECRET_KEY;
env S3_REGION;
env MANGOPAY_SANDBOX;
env MANGOPAY_CLIENT_ID;
env MANGOPAY_CLIENT_PASSPHRASE;
env LEGAL_USER_ID;
env REDIS_URL;
env EMAIL_ADMIN;
env EMAIL_FROM;
env EMAIL_DOMAIN;
env EMAIL_HOST;
env EMAIL_PORT;
env EMA<PERSON>_USERNAME;
env EMAIL_PASSWORD;
env AUTHY_ENABLED;
env AUTHY_URL;
env AUTHY_KEY;
env EMAIL_OCTOPUS_API_KEY;
env EMAIL_OCTOPUS_LIST_ID;
env FLG_BASE_URL;
env FLG_UPSERT_ENDPOINT;
env FLG_API_KEY;
env FLG_LEAD_GROUP_ID;
env FLG_SITE_ID;
env FLG_CSV_FILE_NAME;
env RECAPTCHA_SITE_KEY;
env RECAPTCHA_SECRET_KEY;
env RECAPTCHA_MINIMUM_SCORE;
env VISIBLE_RECAPTCHA_SITE_KEY;
env VISIBLE_RECAPTCHA_SECRET_KEY;
