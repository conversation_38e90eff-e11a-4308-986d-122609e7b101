# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 2024_10_15_133310) do

  create_table "active_storage_attachments", charset: "utf8mb3", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", charset: "utf8mb3", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum", null: false
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "certification_answers", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "question_id"
    t.string "answer"
    t.boolean "correct", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["question_id"], name: "index_certification_answers_on_question_id"
  end

  create_table "certification_levels", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position"
    t.boolean "default", default: false
    t.string "kind"
    t.text "brief"
    t.string "attachment_file_name"
    t.string "attachment_content_type"
    t.bigint "attachment_file_size"
    t.datetime "attachment_updated_at"
  end

  create_table "certification_questions", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "level_id"
    t.string "question"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["level_id"], name: "index_certification_questions_on_level_id"
  end

  create_table "investment_documents", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "attachment_file_name"
    t.string "attachment_content_type"
    t.bigint "attachment_file_size"
    t.datetime "attachment_updated_at"
  end

  create_table "mandates", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "user_id"
    t.string "bank_account_id"
    t.integer "amount"
    t.string "mangopay_mandate_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "last_payment_date"
    t.datetime "cancelled_at"
    t.integer "day", default: 1
    t.index ["amount"], name: "index_mandates_on_amount"
    t.index ["bank_account_id"], name: "index_mandates_on_bank_account_id"
    t.index ["mangopay_mandate_id"], name: "index_mandates_on_mangopay_mandate_id"
  end

  create_table "noticed_events", charset: "utf8mb3", force: :cascade do |t|
    t.string "type"
    t.string "record_type"
    t.bigint "record_id"
    t.json "params"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.integer "notifications_count"
    t.datetime "seen_at"
    t.index ["record_type", "record_id"], name: "index_noticed_events_on_record"
  end

  create_table "noticed_notifications", charset: "utf8mb3", force: :cascade do |t|
    t.string "type"
    t.bigint "event_id", null: false
    t.string "recipient_type", null: false
    t.bigint "recipient_id", null: false
    t.datetime "read_at"
    t.datetime "seen_at"
    t.datetime "created_at", precision: 6, null: false
    t.datetime "updated_at", precision: 6, null: false
    t.index ["event_id"], name: "index_noticed_notifications_on_event_id"
    t.index ["recipient_type", "recipient_id"], name: "index_noticed_notifications_on_recipient"
  end

  create_table "payment_logs", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.string "mangopay_id"
    t.string "kind"
    t.string "status"
    t.datetime "successful_at"
    t.datetime "failed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "user_id"
    t.string "direction"
    t.integer "amount"
    t.integer "potential_investment_id"
    t.integer "dividend_id"
    t.index ["kind", "status"], name: "index_payment_logs_on_kind_and_status"
    t.index ["potential_investment_id"], name: "index_payment_logs_on_potential_investment_id"
    t.index ["user_id"], name: "index_payment_logs_on_user_id"
  end

  create_table "potential_investment_items", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "potential_investment_id"
    t.integer "property_id"
    t.integer "quantity", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["potential_investment_id"], name: "index_potential_investment_items_on_potential_investment_id"
    t.index ["property_id"], name: "index_potential_investment_items_on_property_id"
  end

  create_table "potential_investments", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_potential_investments_on_user_id"
  end

  create_table "properties", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.string "postcode"
    t.integer "property_amount"
    t.integer "share_count"
    t.float "hpi"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "funded", default: false
    t.string "wallet_id"
    t.integer "property_fee_stamp_duty", default: 0
    t.integer "property_fee_legal_and_professional", default: 0
    t.integer "property_fee_pre_let_expenses", default: 0
    t.integer "property_fee_repairs_provision", default: 0
    t.integer "property_fee_deferred_tax", default: 0
    t.integer "rental_fee_management", default: 0
    t.integer "rental_fee_insurance", default: 0
    t.integer "rental_fee_allowance_for_voids", default: 0
    t.integer "rental_fee_maintenance_allowance", default: 0
    t.integer "rental_fee_corporation_tax", default: 0
    t.integer "rental_fee_deferred_fees", default: 0
    t.integer "rent_amount", default: 0
    t.text "investment_case_and_risk"
    t.boolean "visible", default: true
    t.string "spv_name"
    t.integer "rental_fee_spv_charge", default: 0
    t.string "hpi_area"
    t.boolean "placeholder", default: false
    t.boolean "easy_exit", default: false
    t.decimal "guaranteed_yield", precision: 30, scale: 10
    t.string "slug"
    t.string "address_1"
    t.string "address_2"
    t.string "city"
    t.string "type"
    t.integer "site_value", default: 0
    t.integer "finance", default: 0
    t.float "annualised_return", default: 0.0
    t.date "estimated_completion_date"
    t.integer "gdv", default: 0
    t.integer "profit", default: 0
    t.string "finance_label"
    t.string "property_amount_label"
    t.string "property_fee_legal_and_professional_label"
    t.string "site_value_label"
    t.integer "term", default: 0
    t.string "thumbnail_label"
    t.string "acknowledgement_pdf_file_name"
    t.string "acknowledgement_pdf_content_type"
    t.bigint "acknowledgement_pdf_file_size"
    t.datetime "acknowledgement_pdf_updated_at"
    t.index ["funded"], name: "index_properties_on_funded"
    t.index ["slug"], name: "index_properties_on_slug"
  end

  create_table "property_certification_levels", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "property_id"
    t.bigint "certification_level_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["certification_level_id"], name: "index_property_certification_levels_on_certification_level_id"
    t.index ["property_id"], name: "index_property_certification_levels_on_property_id"
  end

  create_table "property_dividends", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "property_id"
    t.integer "user_id"
    t.integer "payout_id"
    t.decimal "amount", precision: 30, scale: 10
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "aasm_state"
    t.text "reason"
    t.string "idempotency_key"
    t.index ["payout_id"], name: "index_property_dividends_on_payout_id"
    t.index ["property_id"], name: "index_property_dividends_on_property_id"
    t.index ["user_id"], name: "index_property_dividends_on_user_id"
  end

  create_table "property_documents", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "property_id"
    t.string "title"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "attachment_file_name"
    t.string "attachment_content_type"
    t.bigint "attachment_file_size"
    t.datetime "attachment_updated_at"
    t.index ["property_id"], name: "index_property_documents_on_property_id"
  end

  create_table "property_floorplans", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "property_id"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "attachment_file_name"
    t.string "attachment_content_type"
    t.bigint "attachment_file_size"
    t.datetime "attachment_updated_at"
    t.index ["property_id"], name: "index_property_floorplans_on_property_id"
  end

  create_table "property_legal_documents", charset: "utf8mb3", force: :cascade do |t|
    t.integer "property_id"
    t.string "title"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "attachment_file_name"
    t.string "attachment_content_type"
    t.bigint "attachment_file_size"
    t.datetime "attachment_updated_at"
    t.index ["property_id"], name: "index_property_legal_documents_on_property_id"
  end

  create_table "property_news_items", charset: "utf8mb3", force: :cascade do |t|
    t.string "title"
    t.text "content"
    t.date "date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "property_id"
    t.bigint "user_id"
    t.index ["property_id"], name: "index_property_news_items_on_property_id"
    t.index ["user_id"], name: "index_property_news_items_on_user_id"
  end

  create_table "property_payout_fees", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "payout_id"
    t.text "description"
    t.integer "amount"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["payout_id"], name: "index_property_payout_fees_on_payout_id"
  end

  create_table "property_payouts", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "property_id"
    t.integer "user_id"
    t.integer "amount"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "aasm_state"
    t.date "start_date"
    t.date "end_date"
    t.string "wallet_id"
    t.string "type"
    t.index ["property_id"], name: "index_property_payouts_on_property_id"
    t.index ["user_id"], name: "index_property_payouts_on_user_id"
  end

  create_table "property_photos", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "property_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "attachment_file_name"
    t.string "attachment_content_type"
    t.bigint "attachment_file_size"
    t.datetime "attachment_updated_at"
    t.index ["property_id"], name: "index_property_photos_on_property_id"
  end

  create_table "property_property_tags", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "property_id"
    t.integer "tag_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["property_id"], name: "index_property_property_tags_on_property_id"
    t.index ["tag_id"], name: "index_property_property_tags_on_tag_id"
  end

  create_table "property_tags", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "attachment_file_name"
    t.string "attachment_content_type"
    t.bigint "attachment_file_size"
    t.datetime "attachment_updated_at"
  end

  create_table "roles", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_roles_on_name"
  end

  create_table "share_logs", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "user_id"
    t.integer "property_id"
    t.integer "buy_order_id"
    t.integer "sell_order_id"
    t.integer "quantity"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "total_amount", default: 0
    t.index ["property_id"], name: "index_share_logs_on_property_id"
    t.index ["user_id"], name: "index_share_logs_on_user_id"
  end

  create_table "share_order_transactions", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "order_id"
    t.integer "user_id"
    t.integer "quantity"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["order_id"], name: "index_share_order_transactions_on_order_id"
  end

  create_table "share_orders", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "user_id"
    t.integer "property_id"
    t.string "wallet_id"
    t.integer "quantity"
    t.integer "total_amount"
    t.string "type"
    t.string "aasm_state"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "reason"
    t.integer "creator_id"
    t.integer "source_user_id"
    t.text "description"
    t.datetime "cancelled_at"
    t.integer "total_fees", default: 0
    t.integer "payout_id"
    t.string "mangopay_id"
    t.index ["aasm_state"], name: "index_share_orders_on_aasm_state"
    t.index ["creator_id"], name: "index_share_orders_on_creator_id"
    t.index ["payout_id"], name: "index_share_orders_on_payout_id"
    t.index ["property_id"], name: "index_share_orders_on_property_id"
    t.index ["source_user_id"], name: "index_share_orders_on_source_user_id"
    t.index ["type"], name: "index_share_orders_on_type"
    t.index ["user_id"], name: "index_share_orders_on_user_id"
  end

  create_table "user_addresses", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "user_id"
    t.string "first_name"
    t.string "last_name"
    t.string "address_number"
    t.string "address_1"
    t.string "address_2"
    t.string "city"
    t.string "region"
    t.string "post_code"
    t.string "country"
    t.string "kind"
    t.date "date_of_birth"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_addresses_on_user_id"
  end

  create_table "user_certification_attempts", charset: "utf8mb3", force: :cascade do |t|
    t.string "state"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.bigint "certification_level_id"
    t.index ["certification_level_id"], name: "index_user_certification_attempts_on_certification_level_id"
    t.index ["user_id"], name: "index_user_certification_attempts_on_user_id"
  end

  create_table "user_kyc_documents", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "user_id"
    t.string "kyc_document_id"
    t.string "kind"
    t.string "state"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type"
    t.index ["type"], name: "index_user_kyc_documents_on_type"
    t.index ["user_id"], name: "index_user_kyc_documents_on_user_id"
  end

  create_table "user_login_attempts", charset: "utf8mb3", force: :cascade do |t|
    t.string "state"
    t.string "ip"
    t.text "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.index ["user_id"], name: "index_user_login_attempts_on_user_id"
  end

  create_table "user_queued_actions", charset: "utf8mb3", force: :cascade do |t|
    t.string "target_type"
    t.bigint "target_id"
    t.integer "amount"
    t.string "type"
    t.string "aasm_state"
    t.string "bank_account_id"
    t.text "message"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.index ["source_type", "source_id"], name: "index_user_queued_actions_on_source_type_and_source_id"
    t.index ["target_type", "target_id"], name: "index_user_queued_actions_on_target_type_and_target_id"
    t.index ["user_id"], name: "index_user_queued_actions_on_user_id"
  end

  create_table "user_roles", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "user_id"
    t.integer "role_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["role_id"], name: "index_user_roles_on_role_id"
    t.index ["user_id"], name: "index_user_roles_on_user_id"
  end

  create_table "user_states", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.integer "user_id"
    t.string "before"
    t.string "after"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "notified_at"
  end

  create_table "users", id: :integer, charset: "utf8mb3", force: :cascade do |t|
    t.string "email"
    t.string "password_digest"
    t.string "first_name"
    t.string "middle_name"
    t.string "last_name"
    t.date "date_of_birth"
    t.string "nationality"
    t.string "country_of_residence"
    t.string "occupation"
    t.integer "income_range"
    t.string "aasm_state"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "authentication_token"
    t.string "confirmation_token"
    t.datetime "last_sign_in_at"
    t.datetime "current_sign_in_at"
    t.string "last_sign_in_ip"
    t.string "current_sign_in_ip"
    t.integer "sign_in_count", default: 0
    t.string "mangopay_id"
    t.integer "certification_level_id"
    t.string "reset_token"
    t.string "title"
    t.string "phone_number"
    t.string "wallet_id"
    t.string "type"
    t.string "legal_type"
    t.string "business_name"
    t.string "business_number"
    t.boolean "call_me", default: false
    t.datetime "confirmed_at"
    t.integer "authy_id"
    t.string "authy_number"
    t.string "experience"
    t.string "employment_status"
    t.string "planned_investment"
    t.string "two_factor_authentication_token"
    t.boolean "marketing"
    t.datetime "locked_at"
    t.integer "failed_attempts", default: 0, null: false
    t.string "onfido_id"
    t.string "referer"
    t.string "referer_host"
    t.datetime "last_active_at"
    t.string "telnyx_id"
    t.boolean "phone_verified", default: false
    t.string "factor_id"
    t.string "email_octopus_id"
    t.string "flg_lead_id"
    t.string "google_uid"
    t.string "apple_uid"
    t.index ["aasm_state"], name: "index_users_on_aasm_state"
    t.index ["apple_uid"], name: "index_users_on_apple_uid"
    t.index ["authentication_token"], name: "index_users_on_authentication_token"
    t.index ["email"], name: "index_users_on_email"
    t.index ["flg_lead_id"], name: "index_users_on_flg_lead_id", unique: true
    t.index ["google_uid"], name: "index_users_on_google_uid"
    t.index ["mangopay_id"], name: "index_users_on_mangopay_id"
    t.index ["referer_host"], name: "index_users_on_referer_host"
    t.index ["reset_token"], name: "index_users_on_reset_token"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "potential_investment_items", "potential_investments"
  add_foreign_key "potential_investment_items", "properties"
  add_foreign_key "potential_investments", "users"
  add_foreign_key "user_addresses", "users"
  add_foreign_key "user_kyc_documents", "users"
end
