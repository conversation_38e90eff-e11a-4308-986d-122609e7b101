# Roles
Role.find_or_create_by!(name: 'administrator')

# Certification Levels
Certification::Level.destroy_all

lipsum = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.
          Aliquam pellentesque lorem vitae massa rutrum dapibus.
          Aenean eu tortor consectetur, egestas augue et, aliquet tortor.
          Aenean et molestie lacus, sit amet ornare orci.'

questions = [{ question: 'Can property prices fall?', answer: true },
             { question: 'Will you always be able to get your money out immediately?', answer: false },
             { question: 'Will the income and growth forecasts always be correct?', answer: false },
             { question: 'Is investment money across a diverse selection of properties a better investment strategy then putting all you money into one property?', answer: true }]

Certification::Level.create!(name: 'Regular Investor', questions_attributes: questions, position: 1, default: true)
Certification::Level.create!(name: 'Sophisticated Investor', description: lipsum, position: 2)
Certification::Level.create!(name: 'High Net Worth Investor', description: lipsum, position: 3)

# Property Tags
file_path = File.join(Rails.root, 'db', 'seeds', 'icons', '*')

Dir.glob(file_path).each do |file|
  tag = Property::Tag.find_or_initialize_by(name: File.basename(file).split('.').first.titleize)
  tag.attachment = File.open(file)
  tag.save
end
