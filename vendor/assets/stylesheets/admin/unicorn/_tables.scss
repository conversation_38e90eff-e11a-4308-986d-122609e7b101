.table {
  background-color: $unicorn-table-background-color;

  th {
    background-color: $unicorn-table-alt-row-color;
    border-bottom: 0 !important;
    color: #666666;
    font-size: 10px;
    height: 36px;
    height: auto;
    padding: 5px 10px 2px;
    text-align: center;
    vertical-align: middle !important;
  }

  &.table-striped tbody > tr:nth-child(2n) > th,
  &.table-striped tbody > tr:nth-child(2n) > td {
    background-color: $unicorn-table-alt-row-color;
  }

  &.table-hover tbody > tr:hover > td {
    background-color: $unicorn-table-alt-row-color;
  }

  &.table-striped {
    td {
      vertical-align: middle !important;

      &:last-of-type {
        text-align: center;
      }
    }
  }
}
