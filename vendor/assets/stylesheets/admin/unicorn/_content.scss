#content {
  background: $unicorn-content-background-color;
  border-bottom-left-radius: 8px;
  border-top-left-radius: 8px;
  margin-left: 205px;
  margin-right: 0;
  margin-top: -39px;
  min-height: 500px;
  position: relative;
  width: auto;
}

@media (max-width: 767px) {
  #content {
    border-radius: 0;
    left: auto;
    margin-left: 0 !important;
    margin-top: 0;
    transition: left .5s;
    z-index: 1;
  }

  .fixed #content {
    padding-top: 67px;
  }

  .menu-open #content {
    left: 205px;
  }
}

// Content Header

#content-header {
  background-color: $unicorn-header-color;
  border-top-left-radius: 8px;
  margin-top: -38px;
  min-height: 75px;
  padding-top: 5px;
  width: 100%;
  z-index: 20;

  h1 {
    color: $unicorn-header-font-color;
    font-size: 28px;
    font-weight: normal;
    margin-left: 20px;
  }

  > .btn-group {
    float: right;
    margin-top: -45px;
    position: absolute;
    right: 20px;
  }
}

@media (max-width: 767px) {
  #content-header {
    height: auto;
    margin-top: 0;
    padding-top: 10px;
    text-align: center;

    h1 {
      display: block;
      margin-left: auto;
      margin-top: 0;
      padding-top: 0 !important;
      padding-top: 15px;
      text-align: center;
      width: 100%;
    }

    > .btn-group {
      left: 0;
      margin: 10px 0;
      right: auto;
    }

    h1,
    > .btn-group {
      float: none;
      position: relative;
    }
  }
}
