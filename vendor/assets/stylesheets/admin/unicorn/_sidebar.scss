#sidebar {
  display: block;
  float: left;
  position: relative;
  width: 205px;
  z-index: 16;

  ul {
    list-style: none;
    margin: 0;
    padding: 0;
    position: absolute;
    width: 205px;

    li {
      display: block;
      position: relative;

      &.active {
        background-color: $unicorn-sidebar-active-link-color;
      }

      a {
        color: $unicorn-sidebar-link-color;
        display: block;
        font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
        font-weight: 300;
        padding: 10px 0 10px 15px;
        text-decoration: none;

        .arrow {
          float: right;
          font-size: 10px;
          margin: 5px 20px 0 -10px;
          transition: 0.3s ease-in-out;
        }

        .label {
          float: right;
          margin: 2px 20px 0 0;
          padding: 3px 5px 2px;
        }
      }

      > li:hover > a,
      > li.active > a {
        color: $unicorn-sidebar-hover-color;
      }

      &.open > a .arrow {
        -ms-transform: rotate(90deg);
        transform: rotate(90deg);
      }

      &.open ul {
        display: block !important;
      }
    }

    ul {
      background-color: $unicorn-sidebar-open-background-color;
      display: none;
      list-style: none;
      margin: 0;
      padding: 0;
      position: relative;

      li {
        a {
          color: $unicorn-sidebar-link-color;
          display: block;
          padding: 10px 0 10px 25px;
          position: relative;

          &:hover {
            color: $unicorn-black-sixty-color;
          }
        }
      }
    }
  }

  i {
    margin-right: 10px;
  }
}

@media (max-width: 767px) {
  #sidebar {
    float: none;
    height: 100%;
    left: 0;
    position: fixed;
    top: 0;
    z-index: 0;
  }
}
