/**
 * Unicorn Admin Template
 * Version 2.2.0
 * Diablo9983 -> <EMAIL>
**/
$(function(){
  var ul = $('#sidebar > ul');
  var ul2 = $('#sidebar > ul > li.open ul');

  // === Resize window related === //
  $(window).on('resize', function() {
    wwidth = $(window).width();

    if(wwidth > 767) {
      ul.css({'display':'block'});

      $('body').removeClass('menu-open');
      $('#sidebar').attr('style','');
      $('#user-nav > ul').css({width:'auto',margin:'0'});
    }

  });

  if($(window).width() <= 767) {
    if($(window).scrollTop() > 35) {
      $('body').addClass('fixed');
    }

    $(window).scroll(function(){
      if($(window).scrollTop() > 35) {
        $('body').addClass('fixed');
      } else {
        $('body').removeClass('fixed');
      }
    });
  }

  if($(window).width() > 767) {
    ul.css({'display':'block'});
  }

  $(document).on('click', '#menu-trigger', function() {
    if($(window).width() <= 767) {
      if($('body').hasClass('menu-open')) {
        $('body').removeClass('menu-open');
      } else {
        $('body').addClass('menu-open');
      }
    }
    return false;
  });

  $(document).on('click', '.submenu > a', function(e) {
    e.preventDefault();
    var submenu = $(this).siblings('ul');
    var li = $(this).parents('li');

    var submenus = $('#sidebar li.submenu ul');
    var submenus_parents = $('#sidebar li.submenu');

    if(li.hasClass('open')) {
      submenu.slideUp();
      li.removeClass('open');
    }
    else {
      submenus.slideUp();
      submenu.slideDown();
      submenus_parents.removeClass('open');
      li.addClass('open');
    }
  });
});
