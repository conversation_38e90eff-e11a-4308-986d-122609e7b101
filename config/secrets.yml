default: &default
  session_timeout: 10
  secret_key_base: <%= ENV["SECRET_KEY_BASE"] %>
  error_environment: <%= ENV["ERROR_ENVIRONMENT"] %>
  frontend_url: <%= ENV["FRONTEND_URL"] %>
  mangopay_sandbox: <%= ENV["MANGOPAY_SANDBOX"] %>
  mangopay_client_id: <%= ENV["MANGOPAY_CLIENT_ID"] %>
  mangopay_client_passphrase: <%= ENV["MANGOPAY_CLIENT_PASSPHRASE"] %>
  legal_user_id: <%= ENV["LEGAL_USER_ID"] %>
  uown_nuapay_account_id: <%=ENV["UOWN_NUAPAY_ACCOUNT_ID"]%>
  email:
    admin_address: <%= ENV["EMAIL_ADMIN"] %>
    from_address: <%= ENV["EMAIL_FROM"] %>
    domain: <%= ENV["EMAIL_DOMAIN"] %>
    host: <%= ENV["EMAIL_HOST"] %>
    port: <%= ENV["EMAIL_PORT"] %>
    username: <%= ENV["EMAIL_USERNAME"] %>
    password: <%= ENV["EMAIL_PASSWORD"] %>
  authy:
    enabled: <%= ENV["AUTHY_ENABLED"] %>
    url: <%= ENV["AUTHY_URL"] %>
    key: <%= ENV["AUTHY_KEY"] %>
  email_octopus:
    api_key: <%= ENV["EMAIL_OCTOPUS_API_KEY"] %>
    list_id: <%= ENV["EMAIL_OCTOPUS_LIST_ID"] %>
  s3:
    access_key: <%= ENV["S3_ACCESS_KEY"] %>
    bucket: <%= ENV["S3_BUCKET"] %>
    region: <%= ENV["S3_REGION"] %>
    secret_key: <%= ENV["S3_SECRET_KEY"] %>
  flg_crm:
    base_url: <%= ENV["FLG_BASE_URL"] %>
    upsert_endpoint: <%= ENV["FLG_UPSERT_ENDPOINT"] %>
    api_key: <%= ENV["FLG_API_KEY"] %>
    lead_group_id: <%= ENV["FLG_LEAD_GROUP_ID"] %>
    site_id: <%= ENV["FLG_SITE_ID"] %>
    csv_file_name: <%= ENV["FLG_CSV_FILE_NAME"] %>
  recaptcha:
    site_key: <%= ENV["RECAPTCHA_SITE_KEY"] %>
    secret_key: <%= ENV["RECAPTCHA_SECRET_KEY"] %>
    minimum_score: <%= ENV["RECAPTCHA_MINIMUM_SCORE"] %>
    visible:
      site_key: <%= ENV["VISIBLE_RECAPTCHA_SITE_KEY"] %>
      secret_key: <%= ENV["VISIBLE_RECAPTCHA_SECRET_KEY"] %>
  nuapay_api_key: <%= ENV["NUAPAY_API_KEY"] %>
  nuapay_endpoint: <%= ENV["NUAPAY_ENDPOINT"] %>
  nuapay_webhook_secret: <%= ENV["NUAPAY_WEBHOOK_SECRET"] %>
  nuapay:
    private_key: <%= ENV["NUAPAY_PRIVATE_KEY"] %>
    kid: <%= ENV["NUAPAY_KID"] %>
    cn: <%= ENV["NUAPAY_CN"] %>
    sandbox_url: <%= ENV["NUAPAY_SANDBOX_URL"] %>
  shufti:
    client_id: <%= ENV["SHUFTI_CLIENT_ID"] %>
    secret_key: <%= ENV["SHUFTI_SECRET_KEY"] %>
    base_url: <%= ENV["SHUFTI_BASE_URL"] %>



development:
  <<: *default
  session_timeout: 240
  secret_key_base: d6bd1faa3dbd4222319f480f9e0e5c977446f75dab39c473c2f3b6d6d5cb99ce3e66f28b61ba0edc686a033d066c7f709f006161c47cea287cf7171bca88c177
  error_environment: 'development'
  frontend_url: <%= ENV["FRONTEND_URL"] || 'http://localhost:3000' %>
  mangopay_sandbox: <%= ENV["MANGOPAY_SANDBOX"] || true %>
  mangopay_client_id: <%= ENV["MANGOPAY_CLIENT_ID"] || 'uown' %>
  mangopay_client_passphrase: <%= ENV["MANGOPAY_CLIENT_PASSPHRASE"] || 'EfAVFk30SZuhjk0aXVSeMFbuOU3be0md0kgWbfyWeJZFhFfVyQ' %>
  legal_user_id: ********
  uown_nuapay_account_id: 'abx6anzdbl'
  email:
    admin_address: <EMAIL>
    from_address: 'UOWN Development<<EMAIL>>'
    domain: uown.cloud
    host: 'email-smtp.eu-west-2.amazonaws.com'
    port: 587
    username: ********************
    password: BIrJzaVCc5lcw1MXTi7DZkoK5TgeEbI9uS1uDVf2kLdc
  authy:
    enabled: false
    url: 'https://api.authy.com'
    key: 'Uk8whKUq9NQJjvzD1O42PJOKVBoOt5VO'
  dotmailer:
    email: 'xxx'
    password: 'xxx'
    address_book: 'uown_development'
  s3:
    access_key: ********************
    bucket: uown-development-webservices
    region: eu-west-2
    secret_key: xBM33ga4DyqeWq8nR717WnEnAR2dhMwj2+60rud2
  flg_crm:
    base_url: <%= ENV["FLG_BASE_URL"] || 'https://parklane.flg360.co.uk/api' %>
    upsert_endpoint: <%= ENV["FLG_UPSERT_ENDPOINT"] || '/APILeadCreateUpdate.php' %>
    api_key: <%= ENV["FLG_API_KEY"] || 'YRqlF9vREMrlbqmjTZJOOHntHQ9EfKuh' %>
    lead_group_id: <%= ENV["FLG_LEAD_GROUP_ID"] || '61560' %>
    site_id: <%= ENV["FLG_SITE_ID"] || '21162' %>
    csv_file_name: <%= ENV["FLG_CSV_FILE_NAME"] || '20240613-120635-ExportedLeads.csv' %>
  email_octopus:
    list_id: <%= ENV["EMAIL_OCTOPUS_LIST_ID"] || '47e4b351-5c2b-11ec-96e5-06b4694bee2a'%>
    api_key:
      email_octopus_api_key: <%= ENV["EMAIL_OCTOPUS_API_KEY"] || 'facde777-673c-43d7-9168-ca588f86026a'%>
  recaptcha:
    site_key: <%= ENV["RECAPTCHA_SITE_KEY"] || '6Lfk7Q8rAAAAAIGbJeN1_dbUm9pEbeSgXRKkbJYY' %>
    secret_key: <%= ENV["RECAPTCHA_SECRET_KEY"] || '6Lfk7Q8rAAAAANpJgyRV8Tgac3ouGhDmMIywNd6l' %>
    minimum_score: <%= ENV["RECAPTCHA_MINIMUM_SCORE"] || 0.9 %>
    visible:
      site_key: <%= ENV["VISIBLE_RECAPTCHA_SITE_KEY"] || '6Lfk7Q8rAAAAAIGbJeN1_dbUm9pEbeSgXRKkbJYY' %>
      secret_key: <%= ENV["VISIBLE_RECAPTCHA_SECRET_KEY"] || '6Lfk7Q8rAAAAANpJgyRV8Tgac3ouGhDmMIywNd6l' %>
  nuapay_api_key: '2ca5723f142fe311135d9de414c83fcd1f13b9a2d8d1b081e12975de30a3e739'
  nuapay_endpoint: 'https://proxy.uown.co/dev/'
  nuapay:
    private_key: 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
    kid: '244316496579'
    cn: 'a2aej7wzbw'
    sandbox_url: 'https://sandbox.nuapay.com'
  shufti:
    client_id: '2a8aa6955c9b34f88fa746d1c57a7da3087153adb24497f3881e9ac41a3cb4ba'
    secret_key: 'Iot52Cz8sx0dRil30L3fNUJpbjiTPFcK'
    base_url: 'https://api.shuftipro.com'


test:
  <<: *default
  session_timeout: 15
  secret_key_base: ea84a2d63ed40176af79d5411d416ad8a442a37076bbf135eea418ca305fcbb26f7920e78635581445e9f59f057b0fa113314d234274c2101c7c69b90dc5754c
  error_environment: 'test'
  frontend_url: 'http://localhost:3000'
  mangopay_sandbox: true
  mangopay_client_id: 'test'
  mangopay_client_passphrase: 'abcdefg'
  legal_user_id: 54321
  email:
    admin_address: '<EMAIL>'
    from_address: '<EMAIL>'
    domain: 'dubitcloud.com'
    host: 'email-smtp.us-east-1.amazonaws.com'
    port: 587
    username: 'AKIAJ74NNTMEZL3XET6Q'
    password: 'ArxfOmvD27b9H9oQ3jtRGAdkdC992ff8/2Ik08BFmmqi'
  authy:
    enabled: true
    url: 'https://api.authy.com'
    key: 'abcdefg12345'
  email_octopus:
    api_key:
      email_octopus_api_key: 'xxx'
    list_id: 'xxx'
    debug: false
  flg_crm:
    base_url: 'https://parklane.flg360.co.uk/api'
    upsert_endpoint: '/APILeadCreateUpdate.php'
    api_key: 'YRqlF9vREMrlbqmjTZJOOHntHQ9EfKuh'
    lead_group_id: '61560'
    site_id: '21162'
    csv_file_name: 'test-flg-users.csv'

production:
  <<: *default
