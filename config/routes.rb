Rails.application.routes.draw do
  root 'sessions#new'

  namespace :admin do
    root 'dashboard#index'
    post 'monthly_report' => 'dashboard#monthly_report'

    resources :certification_levels, except: [:show, :destroy] do
      put :reorder, on: :collection
    end

    resources :certification_attempts, only: [:index], module: :user
    resources :investment_documents, except: [:show]
    resources :login_attempts, only: [:index], module: :user
    resources :queued_actions, only: [:index], module: :user
    resources :portfolio_items, only: [:index], module: :user

    resources :mandates, only: [:index]
    resources :notifications, only: [:index, :destroy] do
      member do
        get :seen
      end
    end
    resources :payment_logs, only: [:index]

    resources :property_causes, :property_developments, :property_loans, :property_regulars, except: [:show, :destroy] do
      put :upgrade, on: :member
    end

    resources :properties, only: [] do
      resources :news_items, except: [:show], module: :property

      namespace :share do
        resources :logs, only: [:index]
        resources :orders, only: [:index]
      end
    end

    namespace :property do
      resources :payout_properties, :payout_rents, only: [:index, :new, :create, :show, :update] do
        member do
          get :confirm_totals
          put :allocate_funds
          get :confirm_dividends
          put :distribute_dividends
        end
        collection do
          get :last_month_amount
        end
      end

      resources :dividends, only: [:index]
      resources :tags, except: [:show]
      resources :nuapay_accounts, only: [:index]
    end

    resources :referals, only: [:index]

    namespace :share do
      resources :easy_exit_orders, only: [:index, :show] do
        member do
          put :accept
          put :reject
        end
      end
      resources :logs, only: [:index]
      resources :orders, only: [:index] do
        delete :cancel, on: :member
      end
      resources :transfer_orders, only: [:index, :new, :create] do
        get :details, on: :collection
      end
    end

    resources :users, except: [:new, :create, :show, :destroy] do
      post :wallet_report, on: :collection
      get :fetch_kyc_documents, on: :member

      namespace :share do
        resources :logs, only: [:index]
        resources :orders, only: [:index]
      end

      resources :kyc_documents, only: [:index, :new, :create], controller: 'user/kyc_documents' do
        put :refresh, on: :collection
      end

      resources :certification_attempts, only: [:index], module: :user
      resources :login_attempts, only: [:index], module: :user
      resources :queued_actions, only: [:index], module: :user

      resources :mandates, only: [:index]
      resources :payment_logs, only: [:index]
      resources :user_states, only: [:index]
    end
  end

  resources :sessions, only: [:new, :create] do
    collection do
      delete :destroy
    end
  end

  namespace :two_factor_auth do
    resource :setup, only: [:new, :create], controller: 'setup'
    resource :validate, only: [:new, :create], controller: 'validate'
  end

  get 'login'    => 'sessions#new',      as: :login
  get 'logout'   => 'sessions#destroy',  as: :logout
end
