require_relative 'boot'

require 'rails/all'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module UownAdmin
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 6.1

    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration can go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded after loading
    # the framework and any gems in your application.
    config.autoload_paths << Rails.root.join('lib')
    config.eager_load_paths << Rails.root.join('lib')

    # Configure generators
    config.generators do |g|
      g.assets false
      g.helper false
    end

    # Amazon SES
    config.action_mailer.delivery_method = :smtp
    config.action_mailer.delivery_job = 'ActionMailer::MailDeliveryJob'
    config.action_mailer.smtp_settings = {
      address: Rails.application.secrets.email[:host],
      port: Rails.application.secrets.email[:port],
      domain: Rails.application.secrets.email[:domain],
      user_name: Rails.application.secrets.email[:username],
      password: Rails.application.secrets.email[:password],
      authentication: 'plain',
      enable_starttls_auto: true
    }
  end
end
