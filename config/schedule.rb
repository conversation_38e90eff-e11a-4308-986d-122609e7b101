job_type :rake, "cd :path && bundle config path /home/<USER>/.bundle && bundle exec rake :task --silent :output"

set :output, '/home/<USER>/webapp/log/whenever.log'

# Schedule direct debit payments
# every :hour, at: 0 do
#   rake 'uown:direct_debits:schedule'
# end

# Check direct debit status
# every :hour, at: 15 do
#   rake 'uown:direct_debits:process'
# end

# Check if bank wires were successful
# every :hour, at: 30 do
#   rake 'uown:bank_wires:process'
# end

# Send marketing data to email octopus
every 1.day do
  rake 'uown:push_to_email_octopus'
end

# Check if user KYC is complete
# every 10.minutes do
#   rake 'uown:kyc:check_status'
# end

# Update the property account status
# every 5.minutes do
#   rake 'uown:property:account_update'
# end

