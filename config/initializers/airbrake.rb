require_relative './filter_parameter_logging'

# Configure Errbit exception monitoring service
Airbrake.configure do |config|
  config.project_id = 572781
  config.project_key = '8c7299c5196629dd482e48c67c3f8cad'

  # Filter out sensitive parameters from those log when an exception occurs
  config.blocklist_keys = Rails.application.config.filter_parameters

  config.environment =  Rails.application.secrets.error_environment

  config.ignore_environments = %w[development test]
end
