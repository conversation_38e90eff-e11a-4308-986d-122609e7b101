:ruby
  path = if @user.present?
           admin_user_share_orders_path(@user, format: 'csv')
         elsif @property.present?
           admin_property_share_orders_path(@property, format: 'csv')
         else
           admin_share_orders_path(format: 'csv')
         end

- content_for :buttons do
  = link_to "#{ icon('download') } Export (CSV)".html_safe, path, class: 'btn btn-primary'

= admin_box 'Search', 'search' do
  = search_form_for @q, url: admin_share_orders_path, html: { method: :get, id: 'admin-advanced-search' } do |f|
    = f.search_field :property_name_or_user_first_name_or_user_last_name_or_user_email_cont, class: 'form-control inline', placeholder: 'Search'
    = f.submit 'Search', class: 'btn btn-default'

= admin_table @title, 'book' do
  %thead
    %th= sort_link(@q, 'property.name', 'Property Name')
    %th= sort_link(@q, 'user.first_name', 'Name')
    %th Kind
    %th Created
    %th Total Amount
    %th Quantity
    %th Quantity Remaining
    %th State
    %th Action
  %tbody
    - @share_orders.each do |share_order|
      %tr{id: "share_order-#{share_order.id}"}
        %td= link_to share_order.property.name, [:edit, :admin, share_order.property]
        %td= link_to share_order.user.display_name, edit_admin_user_path(share_order.user)
        %td= share_order.type.split('::').last
        %td= share_order.created_at
        %td.center= currency(share_order.total_amount)
        %td.center= share_order.quantity
        %td.center= share_order.quantity_remaining
        %td.center= share_order.aasm_state.titleize
        %td.center
          - if share_order.cancellable?
            = link_to "#{icon('remove')} Cancel".html_safe, cancel_admin_share_order_path(share_order), method: :delete,
                                                                                                        class: 'btn btn-danger',
                                                                                                        data: { confirm: 'Are you sure?' }
    - if @share_orders.none?
      %tr
        %td.center{colspan: '9'} Sorry: There are currently no share orders to display.

= paginate @share_orders
