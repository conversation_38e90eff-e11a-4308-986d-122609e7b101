= f.select :property_id, Property.pluck(:name, :id), include_blank: true
= f.select :source_user_id, users_for_select_field, label: 'From', help: 'Loading...'
= f.select :user_id, users_for_select_field, include_blank: true, label: 'To', help: 'Loading...'
= f.number_field :quantity
= f.number_field :quantity_confirmation, label: 'Confirm Quantity'
= f.text_area :description
= f.text_field :creator, value: current_user.email, disabled: true, label: 'Initiated By'

:javascript
  $('#share_transfer_order_property_id').on('change', function(e) {
    $('#share_transfer_order_source_user_id, #share_transfer_order_user_id').trigger('change');
  });

  $('#share_transfer_order_source_user_id, #share_transfer_order_user_id').on('change', function(e) {
    var element = $(this);
    var help_element = element.parent().find('.help-block');
    var property_id = $('#share_transfer_order_property_id').val();
    var user_id = element.val();
    var params = { 'user_id': user_id, 'property_id': property_id }

    help_element.html('Loading...');

    if (property_id != '' && user_id != '') {
      $.get("#{ details_admin_share_transfer_orders_path }", params, function(data) {
        html = "Name: " + data.name + "<br />Email: " + data.email + "<br />Shares Owned: " + data.shares_available;
        help_element.html(html);
      });
    }
  });
