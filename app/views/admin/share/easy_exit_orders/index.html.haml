= admin_box 'Search', 'search' do
  = search_form_for @q, url: admin_share_easy_exit_orders_path, html: { method: :get, id: 'admin-advanced-search' } do |f|
    = f.search_field :property_name_or_user_first_name_or_user_last_name_or_user_email_cont, class: 'form-control inline', placeholder: 'Search'
    = f.submit 'Search', class: 'btn btn-default'

= admin_table @title, 'book' do
  %thead
    %th= sort_link(@q, 'property.name', 'Property Name')
    %th= sort_link(@q, 'user.first_name', 'Name')
    %th Quantity
    %th Amount
    %th Fees
    %th Created
    %th State
    %th.action-2 Action
  %tbody
    - @share_easy_exit_orders.each do |share_easy_exit_order|
      %tr{id: "share_easy_exit_order-#{share_easy_exit_order.id}"}
        %td= link_to share_easy_exit_order.property.name, [:edit, :admin, share_easy_exit_order.property]
        %td= link_to share_easy_exit_order.user.display_name, edit_admin_user_path(share_easy_exit_order.user)
        %td.center= share_easy_exit_order.quantity
        %td.center= currency(share_easy_exit_order.total_amount)
        %td.center= currency(share_easy_exit_order.total_fees)
        %td.center= share_easy_exit_order.created_at
        %td.center= share_easy_exit_order.aasm_state.humanize
        %td
          - if share_easy_exit_order.pending?
            = link_to "#{icon('search')} Accept / Reject".html_safe, admin_share_easy_exit_order_path(share_easy_exit_order),
                                                                     class: 'btn btn-info'

    - if @share_easy_exit_orders.none?
      %tr
        %td.center{colspan: '8'} Sorry: There are currently no easy exit orders to display.

= paginate @share_easy_exit_orders
