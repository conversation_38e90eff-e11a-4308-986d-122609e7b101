= admin_box 'Property Wallet', 'book' do
  %ul.site-stats
    %li
      = icon('piggy-bank')
      Before:
      = currency(@share_easy_exit_order.property.wallet.balance)
    %li
      = icon('piggy-bank')
      After:
      = currency(@share_easy_exit_order.property.wallet.balance - @share_easy_exit_order.total_before_fees)

= admin_box 'Details', 'book' do
  %ul.site-stats
    %li
      = icon('user')
      User:
      = link_to @share_easy_exit_order.user.display_name, edit_admin_user_path(@share_easy_exit_order.user)
    %li
      = icon('home')
      Property:
      = link_to @share_easy_exit_order.property.name, edit_admin_user_path(@share_easy_exit_order.property)
    %li
      = icon('piggy-bank')
      Amount Before Fees:
      = currency(@share_easy_exit_order.total_before_fees)
    %li
      = icon('piggy-bank')
      Fees (#{Share::EasyExitOrder::FEE_PERCENTAGE * 100}%):
      = currency(@share_easy_exit_order.calculated_fees)
    %li
      = icon('piggy-bank')
      Total After Fees:
      = currency(@share_easy_exit_order.total_amount)
    %li
      = icon('calendar')
      Created On:
      = @share_easy_exit_order.created_at

  - if @share_easy_exit_order.pending?
    = link_to "#{icon('ok')} Accept".html_safe, accept_admin_share_easy_exit_order_path(@share_easy_exit_order),
                                                class: 'btn btn-primary',
                                                method: :put,
                                                data: { confirm: 'Are you sure you want to accept this order?' }

    = link_to "#{icon('remove')} Reject".html_safe, reject_admin_share_easy_exit_order_path(@share_easy_exit_order),
                                                    class: 'btn btn-danger',
                                                    method: :put,
                                                    data: { confirm: 'Are you sure you want to reject this order?' }
