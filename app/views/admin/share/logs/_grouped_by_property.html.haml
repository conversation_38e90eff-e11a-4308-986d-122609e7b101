= admin_table "Grouped By Property", 'book' do
  %thead
    %th Property
    %th Quantity
  %tbody
    - grouped_logs.each do |share_log|
      %tr{id: "share_log-#{share_log.id}"}
        %td= link_to share_log.property.name, [:edit, :admin, share_log.property]
        %td= share_log.total
    - if grouped_logs.none?
      %tr
        %td.center{colspan: '3'} Sorry: This user doesn't currently have any shares.
