:ruby
  path = if @user.present?
           admin_user_share_logs_path(@user, format: 'csv')
         elsif @property.present?
           admin_property_share_logs_path(@property, format: 'csv')
         else
           admin_share_logs_path(format: 'csv')
         end

- content_for :buttons do
  = link_to "#{ icon('download') } Export (CSV)".html_safe, path, class: 'btn btn-primary'

= render partial: 'grouped_by_user', locals: { grouped_logs: @grouped_by_user } if @grouped_by_user
= render partial: 'grouped_by_property', locals: { grouped_logs: @grouped_by_property } if @grouped_by_property

= admin_table @title, 'book' do
  %thead
    %th Property
    %th User
    %th Created
    %th Quantity
  %tbody
    - @share_logs.each do |share_log|
      %tr{id: "share_log-#{share_log.id}"}
        %td= link_to share_log.property.name, [:edit, :admin, share_log.property]
        %td= link_to share_log.user.display_name, edit_admin_user_path(share_log.user)
        %td= share_log.created_at
        %td= share_log.quantity
    - if @share_logs.none?
      %tr
        %td.center{colspan: '4'} Sorry: There are currently no share logs to display.

= paginate @share_logs
