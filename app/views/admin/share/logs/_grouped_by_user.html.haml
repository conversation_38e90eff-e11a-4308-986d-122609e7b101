= admin_table "Grouped By User", 'book' do
  %thead
    %th User
    %th Quantity
  %tbody
    - grouped_logs.each do |share_log|
      %tr{id: "share_log-#{share_log.id}"}
        %td= link_to share_log.user.display_name, edit_admin_user_path(share_log.user)
        %td= share_log.total
    - if grouped_logs.none?
      %tr
        %td.center{colspan: '3'} Sorry: This property doesn't currently have any shares.
