= admin_box 'Search', 'search' do
  = form_for :search, url: admin_referals_path, html: { method: :get } do |f|
    Between
    = date_field_tag :start_date, @start_date.strftime('%Y-%m-%d'), class: 'form-control datepicker'
    and
    = date_field_tag :end_date, @end_date.strftime('%Y-%m-%d'), class: 'form-control datepicker'

    = submit_tag 'Search', class: 'btn btn-default'

= admin_table @title, 'magnet' do
  %thead
    %th Referrer
    %th Count
  - @referrals.each do |referal|
    %tr
      %td= link_to referal.referer_host, admin_users_path(user: { referer_host: referal.referer_host })
      %td= referal.total

= paginate @referrals
