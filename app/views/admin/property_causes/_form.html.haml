= f.text_field :name

- unless f.object.new_record?
  = f.text_field :slug, disabled: true

= f.text_area :description, help: markdown_help
= f.number_field :property_amount_in_pounds, min: 0, step: 'any', prepend: '£', label: 'Target Amount'
= f.number_field :share_count, disabled: !f.object.new_record?

= f.select :tag_ids, Property::Tag.pluck(:name, :id), { label: 'Tags' }, { multiple: true }

= render 'admin/property/shared/labels', f: f
= render 'admin/property/shared/photos', f: f
= render 'admin/property/shared/documents', f: f
= render 'admin/property/shared/certification_levels', f: f
= render 'admin/property/shared/placeholder', f: f

= f.form_group :visible, label: { text: 'Visible' }  do
  = f.check_box :visible, label: 'Is this cause visible on the causes page?'
