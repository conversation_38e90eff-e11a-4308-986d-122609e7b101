- content_for :buttons do
  = link_to "#{ icon('download') } Export (CSV)".html_safe, admin_portfolio_items_path(format: 'csv'), class: 'btn btn-primary'

= admin_table "Porfolio", 'file' do
  %thead
    %th User ID
    %th User Email
    %th User Name
    %th Property Name
    %th Total Quantity
  %tbody
    - @portfolio_items.each do |item|
      %tr
        %td= item.user_id
        %td= link_to item.user.email, edit_admin_user_path(item.user)
        %td= item.user.display_name
        %td= link_to item.property.name, [:edit, :admin, item.property]
        %td= item.total

    - if @portfolio_items.none?
      %tr
        %td.center{ colspan: '7' } Sorry: No items to display.

= paginate @portfolio_items
