= admin_table @title, 'file' do
  %thead
    %th User
    %th Created
    %th State
    %th Ip Address
    %th City
    %th Country
    %th User Agent
  %tbody
    - @login_attempts.each do |login_attempt|
      - location = @geocoder.lookup(login_attempt.ip) if login_attempt.ip.present?

      %tr{id: "login_attempt-#{login_attempt.id}"}
        %td= link_to login_attempt.user.display_name, edit_admin_user_path(login_attempt.user)
        %td= login_attempt.created_at
        %td= login_attempt.state.capitalize
        %td= login_attempt.ip
        %td= location.country.name if location && location.found?
        %td= location.city.name if location && location.found?
        %td= login_attempt.user_agent

    - if @login_attempts.none?
      %tr
        %td.center{ colspan: '7' } Sorry: This user has no login attempts to display.

= paginate @login_attempts
