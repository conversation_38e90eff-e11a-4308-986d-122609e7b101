- content_for :buttons do
  - if @user.kyc_light_is_complete? || @user.kyc_regular_is_complete? || ( @user.kyc_regular_has_failed? || @user.kyc_regular_is_required? )
    = link_to "#{icon('file')} Add Document/s".html_safe, new_admin_user_kyc_document_path(@user),
                                                          class: 'btn btn-primary'
  - elsif @user.kyc_regular_is_pending?
    = link_to "#{icon('refresh')} Refresh".html_safe, refresh_admin_user_kyc_documents_path(@user),
                                                      method: :put,
                                                      class: 'btn btn-warning'

= admin_table @title, 'file' do
  %thead
    %th Kind
    %th State
    %th Remote ID
    %th Provider
    %th Created
    %th Updated
  %tbody
    - @kyc_documents.each do |kyc_document|
      %tr{id: "kyc_document-#{kyc_document.id}"}
        %td= kyc_document.kind&.humanize
        %td.center= kyc_document.state.humanize
        %td.center= kyc_document.kyc_document_id
        %td.center= kyc_document.is_a?(User::MangopayKycDocument) ? 'Mangopay' : 'Shufti'
        %td.center= kyc_document.created_at
        %td.center= kyc_document.updated_at

    - if @kyc_documents.none?
      %tr
        %td.center{colspan: '5'} Sorry: This user has no kyc documents to display.

= paginate @kyc_documents
