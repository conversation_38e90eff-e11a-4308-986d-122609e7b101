- content_for :buttons do
  - path = @user ? admin_user_certification_attempts_path(@user, format: :csv) : admin_certification_attempts_path(format: :csv)
  = link_to "#{icon('download')} Export (CSV)".html_safe, path, class: 'btn btn-primary'

= admin_table @title, 'file' do
  %thead
    %th User
    %th Certification
    %th State
    %th Created
  %tbody
    - @certification_attempts.each do |certification_attempt|
      %tr{id: "certification_attempt-#{certification_attempt.id}"}
        %td= link_to certification_attempt.user.display_name, edit_admin_user_path(certification_attempt.user)
        %td= certification_attempt.certification_level.name
        %td= certification_attempt.state.capitalize
        %td= certification_attempt.created_at

    - if @certification_attempts.none?
      %tr
        %td.center{ colspan: '4' } Sorry: This user has no certification attempts to display.

= paginate @certification_attempts
