= admin_table @title, 'bookmark' do
  %thead
    %th User
    %th Waiting On
    %th Kind
    %th Details
    %th State
    %th Created
    %th Updated
  %tbody
    - @queued_actions.each do |queued_action|
      %tr{id: "queued_action-#{queued_action.id}"}
        %td= link_to queued_action.user.display_name, edit_admin_user_path(queued_action.user)
        %td= trigger_for_queued_action(queued_action)
        %td= queued_action.is_a?(User::QueuedActionInvestment) ? 'Investment' : 'Payout'
        %td
          - if queued_action.is_a?(User::QueuedActionInvestment)
            - queued_action.target&.items&.each do |item|
              %span= "#{item.property.name} - Quantity: #{item.quantity}"
              %br
          - else
            = currency(queued_action.amount)

        %td
          = queued_action.aasm_state.humanize
          - if queued_action.failed?
            (#{queued_action.message})

        %td= queued_action.created_at
        %td= queued_action.updated_at

    - if @queued_actions.none?
      %tr
        %td.center{ colspan: '7' } Sorry: This user has no queued actions to display.

= paginate @queued_actions
