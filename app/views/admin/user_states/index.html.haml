= admin_table @title, 'list' do
  %thead
    %th Before
    %th After
    %th Date
    %th Notified
  %tbody
    - @user_states.each do |user_state|
      %tr{id: "user_state-#{user_state.id}"}
        %td= user_state.before.titleize rescue ''
        %td= user_state.after.titleize
        %td= user_state.created_at
        %td= user_state.notified_at
    - if @user_states.none?
      %tr
        %td.center{colspan: '4'} Sorry: There are currently no timestamps to display.
