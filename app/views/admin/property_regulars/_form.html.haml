= f.text_field :name

- unless f.object.new_record?
  = f.text_field :slug, disabled: true

= f.text_field :spv_name
= f.text_area :description, help: markdown_help

= f.form_group label: { text: 'Address Details' } do
  = f.text_field :address_1
  = f.text_field :address_2
  = f.text_field :city
  = f.text_field :postcode

= f.text_area :investment_case_and_risk, help: markdown_help

= f.number_field :property_amount_in_pounds, min: 0, step: 'any', prepend: '£', label: 'Property amount'
= f.number_field :rent_amount_in_pounds, min: 0, step: 'any', prepend: '£', label: 'Rent amount'
= f.number_field :share_count, disabled: !f.object.new_record?

= render partial: 'property_fees', locals: { f: f }
= render partial: 'rental_fees', locals: { f: f }

= f.number_field :guaranteed_yield, step: '0.01', min: 0.0, help: 'Leave blank to ignore'

= f.number_field :hpi, step: '0.01', min: 0.0
= f.text_field :hpi_area

= f.select :tag_ids, Property::Tag.pluck(:name, :id), { label: 'Tags' }, { multiple: true }

= render 'admin/property/shared/labels', f: f
= render 'admin/property/shared/photos', f: f
= render 'admin/property/shared/documents', f: f
= render 'admin/property/shared/floorplans', f: f
= render 'admin/property/shared/legal_documents', f: f
= render 'admin/property/shared/certification_levels', f: f
= render 'admin/property/shared/placeholder', f: f

- unless f.object.placeholder?
  = f.form_group :easy_exit, label: { text: 'Easy exit' } do
    = f.check_box :easy_exit, label: 'Mark this property as easy exit'

= f.form_group :visible, label: { text: 'Visible' }  do
  = f.check_box :visible, label: 'Is this property visible on the investment page?'
