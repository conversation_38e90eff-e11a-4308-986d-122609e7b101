= f.form_group label: { text: 'Headquarters' } do
  #headquarters
    = f.fields_for :headquarters do |a|
      = a.hidden_field :id
      = a.hidden_field :kind, value: User::Address::HEADQUARTERS

      = a.text_field :address_number
      = a.text_field :address_1
      = a.text_field :address_2
      = a.text_field :city

      = a.form_group label: { text: 'Country' } do
        = a.country_select :country, { include_blank: true }, { class: 'form-control' }

      = a.text_field :post_code

      %p.right= a.link_to_remove 'Remove', class: 'btn btn-danger'

  = f.link_to_add 'Add Headquarters', :headquarters, class: 'btn btn-primary', data: { target: '#headquarters', limit: 1 }
