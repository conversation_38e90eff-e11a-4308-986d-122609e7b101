= f.form_group label: { text: 'Directors' } do
  #directors
    = f.fields_for :directors do |a|
      = a.hidden_field :id
      = a.hidden_field :kind, value: User::Address::DIRECTOR

      = a.text_field :first_name
      = a.text_field :last_name
      = a.date_field :date_of_birth
      = a.text_field :address_number
      = a.text_field :address_1
      = a.text_field :address_2
      = a.text_field :city

      = a.form_group label: { text: 'Country' } do
        = a.country_select :country, { include_blank: true }, { class: 'form-control' }

      = a.text_field :post_code

      %p.right= a.link_to_remove 'Remove', class: 'btn btn-danger'

  = f.link_to_add 'Add Director', :directors, class: 'btn btn-primary', data: { target: '#directors' }
