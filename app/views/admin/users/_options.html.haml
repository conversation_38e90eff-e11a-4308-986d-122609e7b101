.btn-group
  %button.btn.btn-warning.dropdown-toggle{ type: 'button', data: { toggle: 'dropdown'} }
    #{icon('menu-hamburger')} User Options
    %span.caret
  %ul.dropdown-menu.dropdown-menu-right{ role: 'menu' }
    %li= link_to "#{icon('list-alt')} Direct Debits".html_safe, admin_user_mandates_path(user)
    %li= link_to "#{icon('file')} KYC Documents".html_safe, admin_user_kyc_documents_path(user)
    %li= link_to "#{icon('piggy-bank')} Payment Logs".html_safe, admin_user_payment_logs_path(user)
    %li= link_to "#{icon('book')} Shares Logs".html_safe, admin_user_share_logs_path(user)
    %li= link_to "#{icon('book')} Shares Orders".html_safe, admin_user_share_orders_path(user)

    %li.divider{ role: 'separator' }
    %li= link_to "#{icon('list')} Timestamps".html_safe, admin_user_user_states_path(user)
    %li= link_to "#{icon('signal')} Login Attempts".html_safe, admin_user_login_attempts_path(user)
    %li= link_to "#{icon('signal')} Certification Attempts".html_safe, admin_user_certification_attempts_path(user)
    %li= link_to "#{icon('bookmark')} Queued Actions".html_safe, admin_user_queued_actions_path(user)

    %li.divider{ role: 'separator' }
    %li= mangopay_user_link(user.mangopay_id) if user.mangopay_id.present?
    %li= mangopay_wallet_link(user.mangopay_id, user.wallet_id) if user.wallet_id.present?

    %li.divider{ role: 'separator' }
    %li= link_to "#{icon('refresh')} Fetch KYC Documents".html_safe, fetch_kyc_documents_admin_user_path(user), data: { confirm: 'Are you sure you want to fetch KYC documents?' }
