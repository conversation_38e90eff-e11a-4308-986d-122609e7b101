= f.text_field :occupation
= f.select :income_range, User::Natural::INCOME_RANGES, include_blank: true
= f.select :employment_status, User::Natural::EMPLOYMENT_STATUS, include_blank: true
= f.select :experience, User::Natural::EXPERIENCE, include_blank: true
= f.select :planned_investment, User::Natural::PLANNED_INVESTMENT, include_blank: true

= render partial: 'admin/users/personal', locals: { f: f }
