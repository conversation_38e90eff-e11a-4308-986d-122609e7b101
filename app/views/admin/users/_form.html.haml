// Vitals
= f.select :title, User::TITLES, include_blank: true
= f.text_field :first_name
= f.text_field :middle_name
= f.text_field :last_name
= f.password_field :password

= f.form_group do
  = f.check_box :call_me, label: 'Call Me'

= f.form_group do
  = f.check_box :marketing, label: 'Receive Marketing Emails?'

= f.text_field :email
= f.date_select :date_of_birth, include_blank: true
= f.text_field :phone_number
= f.select :certification_level_id, Certification::Level.pluck(:name, :id), include_blank: true

- if f.object.instance_of?(User::Natural)
  = render partial: 'natural', locals: { f: f }
- else
  = render partial: 'legal', locals: { f: f }

= f.select :role_ids, Role.pluck(:name, :id), { label: 'Roles' }, { multiple: true }

// Debug

- unless f.object.new_record?
  = f.text_field :confirmed_at, disabled: true
  = f.text_field :last_sign_in_at, disabled: true
  = f.text_field :current_sign_in_at, disabled: true
  = f.text_field :last_sign_in_ip, disabled: true
  = f.text_field :current_sign_in_ip, disabled: true
  = f.text_field :sign_in_count, disabled: true
  = f.text_field :referer, disabled: true
  = f.text_field :referer_host, disabled: true
  = f.text_field :mangopay_id, disabled: true, label: 'Mangopay ID'
  = f.text_field :aasm_state, disabled: true, label: 'State'
