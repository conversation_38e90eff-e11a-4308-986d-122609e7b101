= admin_box 'Search', 'search' do
  = search_form_for @q, url: admin_users_path, html: { method: :get, id: 'admin-advanced-search' } do |f|
    = f.search_field :email_or_first_name_or_last_name_cont, class: 'form-control inline', placeholder: 'Search'
    = f.submit 'Search', class: 'btn btn-default'
    = submit_tag 'Export Results (Csv)', name: 'export_csv', class: 'btn btn-default', data: { confirm: 'Are you sure? This will take a few moments' }

= admin_box 'Reports', 'file' do
  = form_for :search, url: wallet_report_admin_users_path, html: { method: :post, id: 'admin-generate-report' } do |f|
    = submit_tag 'User Balances (Csv)', name: 'email_csv', class: 'btn btn-default', data: { confirm: 'Are you sure to generate user wallet report?' }

= admin_table @title, 'user' do
  %thead
    %th= sort_link(@q, :name)
    %th= sort_link(@q, :email)
    %th Call Me?
    %th Confirmed
    %th Registered
    %th Last Login
    %th State
    %th Kind
    %th Certification
    %th.action-1 Action
  %tbody
    - @users.each do |user|
      %tr{id: "user-#{user.id}"}
        %td= user.full_name
        %td= user.email
        %td= user.call_me? ? 'Yes' : 'No'
        %td= user.confirmed_at.present? ? 'Yes' : 'No'
        %td= user.created_at
        %td= user.current_sign_in_at
        %td= user.aasm_state.titleize
        %td= user.kind
        %td= user.certification
        %td.center= edit_button(edit_admin_user_path(user))
    - if @users.none?
      %tr
        %td.center{colspan: '10'} Sorry: There are currently no users to display.

= paginate @users
