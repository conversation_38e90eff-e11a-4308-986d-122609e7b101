= f.form_group label: { text: 'Address' } do
  #address
    = f.fields_for :address do |a|
      = a.hidden_field :id
      = a.hidden_field :kind, value: User::Address::PERSONAL

      = a.text_field :address_number
      = a.text_field :address_1
      = a.text_field :address_2
      = a.text_field :city

      = a.form_group label: { text: 'Country' } do
        = a.country_select :country, { include_blank: true }, { class: 'form-control' }

      = a.text_field :post_code

      %p.right= a.link_to_remove 'Remove', class: 'btn btn-danger'

  = f.link_to_add 'Add Address', :address, class: 'btn btn-primary', data: { target: '#address', limit: 1 }
