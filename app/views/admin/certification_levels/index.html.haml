- content_for :buttons do
  = link_to "#{ icon('plus') } Add New".html_safe, new_admin_certification_level_path, class: 'btn btn-primary'

= form_for :certification_levels, url: reorder_admin_certification_levels_path, method: :put do |f|
  = admin_table @title, 'signal' do
    %thead
      %th.resize= icon('resize-vertical')
      %th.resize Position
      %th Name
      %th Questions
      %th Kind
      %th Default
      %th.action Action

    %tbody.sortable
      - @certification_levels.each do |certification_level|
        %tr{ id: "certification_level-#{certification_level.id}" }
          %td.center= icon('move')
          %td.center
            = text_field("certification_level[#{certification_level.id}]", 'position', size: 3, maxsize: 3, class: 'index')
            %span.index
          %td= certification_level.name
          %td.center= certification_level.questions.count
          %td.center= certification_level.kind.capitalize
          %td.center= certification_level.default? ? 'Yes' : 'No'
          %td
            = edit_button(certification_level)

    %tfoot
      %tr
        - if @certification_levels.size == 0
          %td.center{ colspan: '7' } Sorry: There are currently no certification levels to display.
        - else
          %td.center{ colspan: '7' }= f.submit 'Save Order', class: 'btn btn-primary'
