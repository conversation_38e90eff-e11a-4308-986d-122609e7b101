= f.form_group label: { text: 'Questions' } do
  %table.table.table-bordered.table-striped.table-hover
    %thead
      %tr
        %th.col-md-8 Question
        %th.col-md-2 Action
    %tbody#questions.nested-form-table
      = f.fields_for :questions, wrapper: false do |q|
        %tr.fields
          %td
            = q.hidden_field :id
            = q.text_field :question

            = render partial: 'answers', locals: { f: q }

          %td
            = q.link_to_remove 'Remove', class: 'btn btn-danger'
    %tfoot
      %tr
        %td
        %td.center
          = f.link_to_add 'Add Question', :questions, class: 'btn btn-primary', data: { target: '#questions' }
