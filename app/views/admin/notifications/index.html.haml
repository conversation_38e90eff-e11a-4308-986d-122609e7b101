= admin_table @title, 'flag' do
  %thead
    %th Notifier
    %th Message
    %th Created At
    %th Created By
    %th Seen At
    %th Action(s)

  %tbody
    - @notifications.each do |notification|
      %tr
        %td= notification.type
        %td= notification.params[:msg]
        %td= notification.created_at
        %td
          - if notification.record
            = link_to notification.record.full_name, edit_admin_user_path(notification.record)
        %td= notification.seen_at
        %td
          = link_to seen_admin_notification_path(notification.id, notifier: notification.type), data: { confirm: 'Are you sure?' }do
            = seen_icon(notification)
            &nbsp;

          = link_to admin_notification_path(notification.id, notifier: notification.type), method: :delete, data: { confirm: 'Are you sure?' } do
            %i.glyphicon.glyphicon-trash

= paginate @notifications
