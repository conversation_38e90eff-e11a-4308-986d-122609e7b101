#sidebar
  %ul
    %li{class: "#{'active' if controller.controller_name == 'dashboard'}"}
      = link_to "#{icon('asterisk')} <span>Dashboard</span>".html_safe, admin_root_path
    %li{class: "#{'active' if controller.controller_name == 'users'}"}
      = link_to "#{icon('user')} <span>Users</span>".html_safe, admin_users_path
    %li{class: "#{'active' if controller.controller_name == 'referals'}"}
      = link_to "#{icon('magnet')} <span>Referrals</span>".html_safe, admin_referals_path
    %li{class: "#{'active' if controller.controller_name == 'property_causes'}"}
      = link_to "#{icon('heart')} <span>Causes</span>".html_safe, admin_property_causes_path

    - properties = %w{ property_developments property_loans property_regulars }

    %li{class: "submenu#{' active open' if properties.include?(controller.controller_name)}"}
      %a{href: "#"}
        = icon("home")
        %span Properties
        %i.arrow.glyphicon.glyphicon-chevron-right
      %ul
        %li= link_to 'Accounts', admin_property_nuapay_accounts_path
        %li= link_to 'Regular', admin_property_regulars_path
        %li= link_to 'Developments', admin_property_developments_path
        %li= link_to 'Loans', admin_property_loans_path

    %li{class: "#{'active' if controller.controller_name == 'payout_rents'}"}
      = link_to "#{icon('book')} <span>Rent/Interest Payments</span>".html_safe, admin_property_payout_rents_path
    %li{class: "#{'active' if controller.controller_name == 'certification_levels'}"}
      = link_to "#{icon('signal')} <span>Certification Levels</span>".html_safe, admin_certification_levels_path
    %li{class: "#{'active' if controller.controller_name == 'tags'}"}
      = link_to "#{icon('tags')} <span>Tags</span>".html_safe, admin_property_tags_path
    %li{class: "#{'active' if controller.controller_name == 'investment_documents'}"}
      = link_to "#{icon('book')} <span>Investment Documents</span>".html_safe, admin_investment_documents_path
    %li{class: "#{'active' if controller.controller_name == 'payout_properties'}"}
      = link_to "#{icon('piggy-bank')} <span>Property Payouts</span>".html_safe, admin_property_payout_properties_path

    - shares = %w{ easy_exit_orders orders logs dividends transfer_orders }

    %li{class: "submenu#{' active open' if shares.include?(controller.controller_name)}"}
      %a{href: "#"}
        = icon("book")
        %span Shares
        %i.arrow.glyphicon.glyphicon-chevron-right
      %ul
        %li= link_to 'Easy Exit Orders', admin_share_easy_exit_orders_path
        %li= link_to 'Orders', admin_share_orders_path
        %li= link_to 'Logs', admin_share_logs_path
        %li= link_to 'Dividends', admin_property_dividends_path
        %li= link_to 'Transfers', admin_share_transfer_orders_path

    %li{class: "#{'active' if controller.controller_name == 'mandates'}"}
      = link_to "#{icon('list-alt')} <span>Direct Debits</span>".html_safe, admin_mandates_path

    - logs = %w{ payment_logs login_attempts certification_attempts queued_actions portfolio_items }

    %li{class: "submenu#{' active open' if logs.include?(controller.controller_name)}"}
      %a{href: "#"}
        = icon("eye-open")
        %span Logs
        %i.arrow.glyphicon.glyphicon-chevron-right
      %ul
        %li= link_to 'Payment Logs', admin_payment_logs_path
        %li= link_to 'Login Attempts', admin_login_attempts_path
        %li= link_to 'Certification Attempts', admin_certification_attempts_path
        %li= link_to 'Queued Actions', admin_queued_actions_path
        %li= link_to 'Portfolio Items', admin_portfolio_items_path

    - flags = %w{ notifications }

    %li{class: "submenu#{' active open' if flags.include?(controller.controller_name)}"}
      %a{href: "#"}
        = icon("flag")
        %span Flags
        %i{class:"badge rounded-pill"}
          = flags_count(nil)
        %i.arrow.glyphicon.glyphicon-chevron-right
      %ul
        %li
          = link_to admin_notifications_path(notifier: 'BankAccountNotifier') do
            Bank Accounts 
            %i.badge.rounded-pill  
              = flags_count('BankAccountNotifier')
        %li
          = link_to admin_notifications_path(notifier: 'ChangeAddressNotifier') do
            Change Address
            %i.badge.rounded-pill
              = flags_count('ChangeAddressNotifier')
        %li
          = link_to admin_notifications_path(notifier: 'LoginAttemptNotifier') do
            Login Attempts
            %i.badge.rounded-pill
              = flags_count('LoginAttemptNotifier')
        %li
          = link_to admin_notifications_path(notifier: 'PayinThresholdNotifier') do
            Payment Threshold
            %i.badge.rounded-pill
              = flags_count('PayinThresholdNotifier')

