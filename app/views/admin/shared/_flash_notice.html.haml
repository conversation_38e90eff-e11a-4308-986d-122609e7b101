- [:notice, :error, :message].each do |name|
  - if flash[name]
    .col-xs-12
      - case name
      - when :notice
        .alert.alert-success
          %button.close{"data-dismiss" => "alert"} ×
          %strong Success!
          = flash[name].html_safe
      - when :error
        .alert.alert-danger
          %button.close{"data-dismiss" => "alert"} ×
          %strong Error!
          = flash[name].html_safe
      - when :message
        .alert.alert-info
          %button.close{"data-dismiss" => "alert"} ×
          %strong Info!
          = flash[name].html_safe
