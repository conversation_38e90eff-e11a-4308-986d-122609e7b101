- content_for :buttons do
  = link_to "#{ icon('plus') } Add New".html_safe, new_admin_investment_document_path, class: 'btn btn-primary'

= admin_table @title, 'book' do
  %thead
    %th Name
    %th File
    %th Size
    %th.action-2 Action

  %tbody
    - @investment_documents.each do |investment_document|
      %tr{ id: "investment_document-#{investment_document.id}" }
        %td= investment_document.name
        %td= link_to(investment_document.attachment_file_name, investment_document.attachment)
        %td= number_to_human_size(investment_document.attachment_file_size)
        %td
          = edit_button(investment_document)
          = delete_button(investment_document)

  %tfoot
    %tr
      - if @investment_documents.size.zero?
        %td.center{ colspan: '4' } Sorry: There are currently no investment documents to display.

= paginate @investment_documents
