= f.text_field :name

- unless f.object.new_record?
  = f.text_field :slug, disabled: true

= f.text_field :spv_name
= f.text_area :description, help: markdown_help

= f.form_group label: { text: 'Address Details' } do
  = f.text_field :address_1
  = f.text_field :address_2
  = f.text_field :city
  = f.text_field :postcode

= f.text_area :investment_case_and_risk, help: markdown_help

= render partial: 'property_fees', locals: { f: f }

= f.number_field :share_count, disabled: !f.object.new_record?

= f.number_field :annualised_return, step: '0.01', min: 0.0

= f.date_field :estimated_completion_date
= f.number_field :term, help: 'in months'

= f.select :tag_ids, Property::Tag.pluck(:name, :id), { label: 'Tags' }, { multiple: true }

= render 'admin/property/shared/labels', f: f
= render 'admin/property/shared/photos', f: f
= render 'admin/property/shared/documents', f: f
= render 'admin/property/shared/floorplans', f: f
= render 'admin/property/shared/legal_documents', f: f
= render 'admin/property/shared/certification_levels', f: f
= render 'admin/property/shared/placeholder', f: f

= f.form_group :visible, label: { text: 'Visible' }  do
  = f.check_box :visible, label: 'Is this property visible on the investment page?'
