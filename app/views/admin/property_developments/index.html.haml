- content_for :buttons do
  = link_to "#{icon('plus')} Add Development".html_safe, [:new, :admin, :property_development], class: 'btn btn-primary'
  = link_to "#{icon('plus')} Add Placeholder Development".html_safe, [:new, :admin, :property_development, placeholder: true], class: 'btn btn-warning'

= render 'admin/shared/search', path: admin_property_developments_path

= admin_table @title, 'home' do
  %thead
    %th.preview Thumbnail
    %th= sort_link(@q, :name)
    %th Available Shares
    %th Share Price
    %th Added
    %th Visible
    %th Funded
    %th Placeholder
    %th.action-4 Action
  %tbody
    - @properties.each do |property|
      %tr{id: "property-#{property.id}", class: ('disabled' unless property.visible?) }
        %td.center
          = image_tag(property.photos.first.attachment.variant(resize: '200x150')) if property.photos.any?
        %td= property.name
        %td.center= property.available_shares
        %td.center= currency(property.share_price, precision: 4)
        %td= property.created_at
        %td.center= property.visible? ? 'Yes' : 'No'
        %td.center= property.funded? ? 'Yes' : 'No'
        %td.center= property.placeholder? ? 'Yes' : 'No'
        %td.center
          - if property.placeholder?
            = link_to "#{icon('arrow-up')} Upgrade".html_safe, upgrade_admin_property_development_path(property), method: :put,
                                                                                                                  class: 'btn btn-warning',
                                                                                                                  data: { confirm: t('properties.confirm_upgrade') }
          - else
            = link_to "#{icon('book')} Logs".html_safe, admin_property_share_logs_path(property), class: 'btn btn-info'
            = link_to "#{icon('book')} Orders".html_safe, admin_property_share_orders_path(property), class: 'btn btn-info'

          = link_to "#{icon('list-alt')} News".html_safe, admin_property_news_items_path(property), class: 'btn btn-warning'
          = edit_button(property)

    - if @properties.none?
      %tr
        %td.center{colspan: '9'} Sorry: There are currently no developments to display.

= paginate @properties
