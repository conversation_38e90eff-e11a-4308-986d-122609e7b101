- content_for :buttons do
  - path = @user.present? ? admin_user_mandates_path(@user, format: 'csv') : admin_mandates_path(format: 'csv')
  = link_to "#{ icon('download') } Export (CSV)".html_safe, path, class: 'btn btn-primary'

= admin_box 'Search', 'search' do
  = search_form_for @q, url: admin_mandates_path, html: { method: :get, id: 'admin-advanced-search' } do |f|
    = f.search_field :user_first_name_or_user_last_name_or_user_email_cont, class: 'form-control inline', placeholder: 'Search'
    = f.submit 'Search', class: 'btn btn-default'

= admin_table @title, 'list-alt' do
  %thead
    %th= sort_link(@q, 'user.first_name', 'User')
    %th Day
    %th Amount
    %th Mangopay ID
    %th Created
    %th Cancelled
  %tbody
    - @mandates.each do |mandate|
      %tr{id: "mandate-#{mandate.id}"}
        %td= link_to mandate.user.display_name, edit_admin_user_path(mandate.user)
        %td.center= mandate.day
        %td.center= currency(mandate.amount)
        %td.center= mandate.mangopay_mandate_id
        %td.center= mandate.created_at
        %td.center= mandate.cancelled_at

    - if @mandates.none?
      %tr
        %td.center{colspan: '6'} Sorry: There are currently no direct debits to display.

= paginate @mandates
