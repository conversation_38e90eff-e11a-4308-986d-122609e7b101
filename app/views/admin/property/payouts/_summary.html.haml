= admin_box 'Summary', 'book' do
  %ul.site-stats
    %li
      = icon('home')
      Property:
      = link_to payout.property.name, [:edit, :admin, payout.property]
    %li
      = icon('piggy-bank')
      Total Profit:
      = currency(payout.amount)
    %li
      = icon('user')
      Created By:
      = link_to payout.user.display_name, edit_admin_user_path(payout.user)
    %li
      = icon('calendar')
      Created On:
      = payout.created_at

  = mangopay_wallet_link(Mango::LegalUser.root.mangopay_id, payout.wallet_id, class: 'btn btn-warning')
