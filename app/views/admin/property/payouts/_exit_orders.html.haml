#exit_orders
  = admin_table 'Order Distribution', 'book' do
    %thead
      %th#distribution User
      %th Amount
      %th Mangopay
      %th Status
    %tbody
      - exit_orders.each do |exit_order|
        %tr
          %td= link_to exit_order.user.display_name, edit_admin_user_path(exit_order.user)
          %td= currency(exit_order.total_amount)

          %td.center
            - if exit_order.mangopay_id.present?
              = mangopay_transfer_link(exit_order.mangopay_id, class: 'btn btn-warning')

          %td.center
            - case exit_order.aasm_state
            - when 'pending', 'active'
              %span.label.label-warning= exit_order.aasm_state.humanize
            - when 'completed'
              %span.label.label-success= exit_order.aasm_state.humanize
            - when 'rejected', 'cancelled'
              %span.label.label-danger= "#{exit_order.aasm_state.humanize} (#{exit_order.reason})"
            - else
              = exit_order.aasm_state.humanize

    - if exit_orders.collect(&:aasm_state).include?('active')
      %tfoot
        %tr
          %td{ colspan: 4 }
            = image_tag('spinner.gif')
            Reloading every 5 seconds

            :javascript
              setTimeout(function(){ $.get(window.location.pathname + '.js'); }, 5000);
