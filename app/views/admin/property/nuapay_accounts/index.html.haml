= admin_table @title, 'piggy-bank' do
  %thead
    %th Name
    %th Remote ID
    %th Status
    %th IBAN
    %th A/C
    %th Category
  %tbody
    - @nuapay_accounts.each do |account|
      %tr
        %td= account.owner_name
        %td= account.remote_id
        %td= account.status
        %td= account.iban
        %td= account.account_number
        - if account.bankable.is_a? User
          %td= link_to account.bankable.display_name, edit_admin_user_path(account.bankable_id)
        - else
          %td= account.bankable.name

    - if @nuapay_accounts.none?
      %tr
        %td.center{colspan: '5'} Sorry: There are currently no NuaPAy accounts to display.

= paginate @nuapay_accounts
