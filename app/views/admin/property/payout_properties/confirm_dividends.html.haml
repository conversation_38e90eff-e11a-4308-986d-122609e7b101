= render 'admin/property/payouts/summary', payout: @object

= admin_table 'Dividends', 'book' do
  %thead
    %th User
    %th Amount
  %tbody
    - @object.dividends.each do |dividend|
      %tr
        %td= link_to dividend.user.display_name, edit_admin_user_path(dividend.user)
        %td.center
          = currency(dividend.amount_to_pay)

= admin_table 'Orders', 'piggy-bank' do
  %thead
    %th User
    %th Quantity
    %th Amount
  %tbody
    - @object.exit_orders.each do |exit_order|
      %tr
        %td= link_to exit_order.user.display_name, edit_admin_user_path(exit_order.user)
        %td= exit_order.quantity
        %td.center
          = currency(exit_order.total_amount)

= admin_table 'Confirm', 'book' do
  %tfoot
    %tr
      %td{ colspan: 4 }=  button_to 'Start Payout',
                                    distribute_dividends_admin_property_payout_property_path(@object),
                                    method: :put,
                                    class: 'btn btn-success',
                                    data: { confirm: 'Are you sure?', disable_with: 'Please Wait' }
