= render 'admin/property/payouts/summary', payout: @object

- total = @object.allocation_amount - @object.amount

= admin_table 'Confirm Totals', 'book' do
  %thead
    %th Description
    %th Amount
  %tbody
    %tr
      %td Dividend Total
      %td= currency(@object.dividends.sum(&:amount_to_pay))
    %tr
      %td Exit Order Total
      %td= currency(@object.exit_orders.sum(&:total_amount))

  %tfoot
    %tr
      %td{ colspan: 3 }= "Total required in root wallet #{currency(@object.allocation_amount)}".html_safe
    %tr
      %td{ colspan: 3 }
        - if @object.funds_available?
          = button_to 'Confirm',
                      allocate_funds_admin_property_payout_property_path(@object),
                      method: :put,
                      class: 'btn btn-success',
                      data: { confirm: 'Are you sure?', disable_with: 'Please Wait' }
        - else
          %p Not enough funds available in root wallet
