- content_for :buttons do
  = link_to "#{icon('plus')} New Payout".html_safe, new_admin_property_payout_property_path, class: 'btn btn-primary'

= admin_box 'Search', 'search' do
  = search_form_for @q, url: admin_property_payout_properties_path, html: { method: :get, id: 'admin-advanced-search' } do |f|
    = f.search_field :property_name_or_user_first_name_or_user_last_name_or_user_email_cont, class: 'form-control inline', placeholder: 'Search'
    = f.submit 'Search', class: 'btn btn-default'

= admin_table @title, 'book' do
  %thead
    %th= sort_link(@q, 'properties.name', 'Property')
    %th= sort_link(@q, 'users.display_name', 'Created By')
    %th Total Profit
    %th.action-3 Action
  %tbody
    - @payout_properties.each do |payout_property|
      %tr{id: "payout_property-#{payout_property.id}"}
        %td= link_to payout_property.property.name, [:edit, :admin, payout_property.property]
        %td= link_to payout_property.user.display_name, edit_admin_user_path(payout_property.user)
        %td.center= currency(payout_property.amount)
        %td.center
          = link_to "#{icon('search')} Details".html_safe,
                    admin_property_payout_property_path(payout_property),
                    class: 'btn btn-default' if payout_property.dividends_distributed?

          = link_to "#{icon('retweet')} Confirm Totals".html_safe,
                    confirm_totals_admin_property_payout_property_path(payout_property),
                    class: 'btn btn-info' if payout_property.pending?

          = link_to "#{icon('retweet')} Confirm Dividends".html_safe,
                    confirm_dividends_admin_property_payout_property_path(payout_property),
                    class: 'btn btn-warning' if payout_property.fees_transferred?

    - if @payout_properties.none?
      %tr
        %td.center{colspan: '4'} Sorry: There are currently no payouts to display.
