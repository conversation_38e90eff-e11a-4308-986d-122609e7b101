- content_for :buttons do
  = link_to "#{ icon('download') } Export (CSV)".html_safe, admin_property_dividends_path(format: 'csv'), class: 'btn btn-primary'

= admin_table @title, 'piggy-bank' do
  %thead
    %th Property
    %th User
    %th Amount
    %th.action-4 State
    %th Created
  %tbody
    - @property_dividends.each do |property_dividend|
      %tr{id: "property_dividend-#{property_dividend.id}"}
        %td= link_to property_dividend.property.name, [:edit, :admin, property_dividend.property]
        %td= link_to property_dividend.user.display_name, edit_admin_user_path(property_dividend.user)
        %td= currency(property_dividend.amount)
        %td
          = property_dividend.aasm_state.capitalize
          - if property_dividend.reason.present?
            "(#{property_dividend.reason})"
        %td= property_dividend.created_at

    - if @property_dividends.none?
      %tr
        %td.center{colspan: '5'} Sorry: There are currently no dividends to display.

= paginate @property_dividends
