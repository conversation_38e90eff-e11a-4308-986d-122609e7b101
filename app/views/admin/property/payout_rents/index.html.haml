- content_for :buttons do
  = link_to "#{icon('plus')} New Payment".html_safe, new_admin_property_payout_rent_path, class: 'btn btn-primary'


= admin_box 'Search', 'search' do
  = search_form_for @q, url: admin_property_payout_rents_path, html: { method: :get, id: 'admin-advanced-search' } do |f|
    = f.search_field :property_name_or_user_first_name_or_user_last_name_or_user_email_cont, class: 'form-control inline', placeholder: 'Search'
    = f.submit 'Search', class: 'btn btn-default'

= admin_table @title, 'book' do
  %thead
    %th= sort_link(@q, 'property.name', 'Property')
    %th= sort_link(@q, 'user.first_name', 'Created By')
    %th From
    %th To
    %th Amount
    %th Subtotal
    %th.action-3 Action
  %tbody
    - @payout_rents.each do |payout_rent|
      %tr{id: "payout_rent-#{payout_rent.id}"}
        %td= link_to payout_rent.property.name, [:edit, :admin, payout_rent.property]
        %td= link_to payout_rent.user.display_name, edit_admin_user_path(payout_rent.user)
        %td= payout_rent.start_date
        %td= payout_rent.end_date
        %td.center= currency(payout_rent.amount)
        %td.center= currency(payout_rent.subtotal)
        %td.center
          - if payout_rent.dividends_distributed?
            = link_to "#{icon('search')} Details".html_safe,
                    admin_property_payout_rent_path(payout_rent),
                    class: 'btn btn-default'
            = link_to "Repeat",
                    new_admin_property_payout_rent_path(id: payout_rent),
                    class: 'btn btn-success'

          = link_to "#{icon('retweet')} Confirm Totals".html_safe,
                    confirm_totals_admin_property_payout_rent_path(payout_rent),
                    class: 'btn btn-info' if payout_rent.pending?

          = link_to "#{icon('retweet')} Confirm Dividends".html_safe,
                    confirm_dividends_admin_property_payout_rent_path(payout_rent),
                    class: 'btn btn-warning' if payout_rent.fees_transferred?

    - if @payout_rents.none?
      %tr
        %td.center{colspan: '7'} Sorry: There are currently no rent payments to display.
