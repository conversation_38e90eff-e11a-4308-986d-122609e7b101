= f.select :property_id, Property.where(placeholder: false).pluck(:name, :id)

= f.date_field :start_date, value: start_date(f)

= f.date_field :end_date

= f.number_field :amount_in_pounds, min: 0, step: 'any', prepend: '£', label: 'Amount', required: true

= render partial: 'fees', locals: { f: f }

= f.hidden_field :user_id, value: current_user.id

.form-group
  %label.control-label.col-sm-2 Subtotal
  .col-sm-10
    .input-group
      %span.input-group-addon £
      %input#subtotal.form-control{ type: 'number', disabled: true, value: 0.00 }

:javascript
  function calculateSubtotal() {
    amount = $('#property_payout_rent_amount_in_pounds').val();

    $('td.line-item input').each(function(i) {
      amount -= $(this).val();
    });

    $('#subtotal').val(parseFloat(amount).toFixed(2));
  }

  $(document).on('blur', '#new_property_payout_rent input', function(e) {
    calculateSubtotal();
  });

  calculateSubtotal();
  $('#property_payout_rent_property_id').on('change', function() {
    console.log('property change');
    $.ajax({
        url: '/admin/property/payout_rents/last_month_amount',
        type: 'GET',
        data: { property_id: this.value },
        success: function(response) {
            console.log('Last Month Amount: ', response.amount);
            $('#property_payout_rent_amount_in_pounds').val(response.amount);
        },
        error: function(xhr, status, error) {
            console.error('An error occurred:', error);
        }
    });
  });