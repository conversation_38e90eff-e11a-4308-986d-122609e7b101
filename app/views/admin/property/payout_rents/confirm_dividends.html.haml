= render 'admin/property/payouts/summary', payout: @object

= admin_table 'Confirm Dividends', 'book' do
  %thead
    %th User
    %th Amount
  %tbody
    - @object.dividends.each do |dividend|
      %tr
        %td= link_to dividend.user.display_name, edit_admin_user_path(dividend.user)
        %td.center
          = currency(dividend.amount_to_pay)

          - if dividend.amount.floor != dividend.amount_to_pay
            (#{currency(dividend.amount_to_pay - dividend.amount.floor)} rolled over)

  %tfoot
    %tr
      %td{ colspan: 4 }=  button_to 'Start Payout',
                                    distribute_dividends_admin_property_payout_rent_path(@object),
                                    method: :put,
                                    class: 'btn btn-success',
                                    data: { confirm: 'Are you sure?', disable_with: 'Please Wait' }
