= render 'admin/property/payouts/summary', payout: @object

- total = @object.allocation_amount - @object.amount

= admin_table 'Confirm Totals', 'book' do
  %thead
    %th Description
    %th Amount
  %tbody
    %tr
      %td Net Rent
      %td= currency(@object.amount)

    %tr
      %td Fees
      %td= currency(@object.total_fees)

    %tr
      %td= total > 0 ? 'Rolled Over Amount' : 'Remainder'
      %td= currency(total > 0 ? total : -total)

  %tfoot
    %tr
      %td{ colspan: 3 }= "Total to transfer from root wallet #{currency(@object.allocation_amount)}".html_safe
    %tr
      %td{ colspan: 3 }
        - if @object.funds_available?
          = button_to 'Confirm',
                      allocate_funds_admin_property_payout_rent_path(@object),
                      method: :put,
                      class: 'btn btn-success',
                      data: { confirm: 'Are you sure?', disable_with: 'Please Wait' }
        - else
          = button_to 'Not enough funds available in root wallet',
                      '#',
                      class: 'btn btn-warning'
