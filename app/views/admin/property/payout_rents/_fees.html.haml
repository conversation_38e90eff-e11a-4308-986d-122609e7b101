= f.form_group label: { text: 'Line Items' } do
  %table.table.table-bordered.table-striped.table-hover
    %thead
      %tr
        %th.col-md-7 Description
        %th.col-md-4 Amount
        %th.col-md-1 Action
    %tbody#fees.nested-form-table
      = f.fields_for :fees, wrapper: false do |i|
        %tr.fields
          %td
            = i.hidden_field :id
            = i.text_field :description, hide_label: true
          %td.line-item= i.number_field :amount_in_pounds, min: 0, step: 'any', prepend: '£', hide_label: true
          %td
            = i.link_to_remove 'Remove', class: 'btn btn-danger'
    %tfoot
      %tr
        %td{ colspan: 2 }
        %td.center
          = f.link_to_add 'Add Line Item', :fees, class: 'btn btn-primary', data: { target: '#fees' }
