- content_for :buttons do
  = link_to "#{icon('plus')} Add Tag".html_safe, new_admin_property_tag_path, class: 'btn btn-primary'

= admin_table @title, 'tags' do
  %thead
    %th.preview Thumbnail
    %th Name
    %th.action-2 Action
  %tbody
    - @property_tags.each do |property_tag|
      %tr{id: "property_tag-#{property_tag.id}"}
        %td.center= image_tag(property_tag.attachment.variant(resize: '44x44'))
        %td= property_tag.name
        %td.center
          = edit_button(property_tag)
          = delete_button(property_tag)
    - if @property_tags.none?
      %tr
        %td.center{colspan: '3'} Sorry: There are currently no tags to display.
