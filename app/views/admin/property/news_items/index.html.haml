- content_for :buttons do
  = link_to "#{icon('plus')} Add News".html_safe, [:new, :admin, :property, :news_item], class: 'btn btn-primary'

= admin_table @title, 'home' do
  %thead
    %th Title
    %th Date
    %th User
    %th.action-2 Action
  %tbody
    - @news_items.each do |news_item|
      %tr{id: "property_news_item-#{news_item.id}" }
        %td= news_item.title
        %td= news_item.date
        %td= link_to news_item.user.full_name, edit_admin_user_path(news_item.user)
        %td.center
          = edit_button(edit_admin_property_news_item_path(@property, news_item))
          = delete_button(admin_property_news_item_path(@property, news_item))

    - if @news_items.none?
      %tr
        %td.center{colspan: '4'} Sorry: There is currently no news to display.

= paginate @news_items
