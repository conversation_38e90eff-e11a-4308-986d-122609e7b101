= f.form_group label: { text: 'Legal Documents' } do
  %table.table.table-bordered.table-striped.table-hover
    %thead
      %tr
        %th.col-md-5 Title
        %th.col-md-5 File
        %th.col-md-2 Action
    %tbody#legal_documents.nested-form-table
      = f.fields_for :legal_documents, wrapper: false do |ld|
        %tr.fields
          %td
            = ld.hidden_field :id
            = ld.text_field :title
          %td.center
            = ld.file_field :attachment, help: 'PDF Format', hide_label: true
            - unless ld.object.new_record?
              Current:
              = link_to ld.object.attachment_file_name, ld.object.attachment
          %td
            = ld.link_to_remove 'Remove', class: 'btn btn-danger'
    %tfoot
      %tr
        %td{ colspan: 2 }
        %td.center
          = f.link_to_add 'Add Legal Document', :legal_documents, class: 'btn btn-primary', data: { target: '#legal_documents' }
