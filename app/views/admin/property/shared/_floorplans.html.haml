= f.form_group label: { text: 'Floorplans' } do
  %table.table.table-bordered.table-striped.table-hover
    %thead
      %tr
        %th.col-md-5 Filename
        %th.col-md-5 File
        %th.col-md-2 Action
    %tbody#floorplans.nested-form-table
      = f.fields_for :floorplans, wrapper: false do |fp|
        %tr.fields
          %td
            = fp.hidden_field :id
            - unless fp.object.new_record?
              = link_to fp.object.attachment_file_name, fp.object.attachment
          %td
            = fp.file_field :attachment, help: 'JPG/PNG/GIF Format', hide_label: true
          %td
            = fp.link_to_remove 'Remove', class: 'btn btn-danger'
    %tfoot
      %tr
        %td{ colspan: 2 }
        %td.center
          = f.link_to_add 'Add Floorplan', :floorplans, class: 'btn btn-primary', data: { target: '#floorplans' }
