= f.form_group label: { text: 'Photos' } do
  %table.table.table-bordered.table-striped.table-hover
    %thead
      %tr
        %th.col-md-5 Image
        %th.col-md-5 File
        %th.col-md-2 Action
    %tbody#photos.nested-form-table
      = f.fields_for :photos, wrapper: false do |p|
        %tr.fields
          %td
            = p.hidden_field :id
            - unless p.object.new_record?
              = image_tag p.object.attachment.variant(resize: '200x150')
          %td
            = p.file_field :attachment, help: 'JPG/PNG/GIF Format', hide_label: true
          %td
            = p.link_to_remove 'Remove', class: 'btn btn-danger'
    %tfoot
      %tr
        %td{ colspan: 2 }
        %td.center
          = f.link_to_add 'Add Photo', :photos, class: 'btn btn-primary', data: { target: '#photos' }
