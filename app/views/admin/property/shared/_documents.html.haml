= f.form_group label: { text: 'Documents' } do
  %table.table.table-bordered.table-striped.table-hover
    %thead
      %tr
        %th.col-md-5 Filename
        %th.col-md-5 File
        %th.col-md-2 Action
    %tbody#documents.nested-form-table
      = f.fields_for :documents, wrapper: false do |d|
        %tr.fields
          %td
            = d.hidden_field :id
            - unless d.object.new_record?
              = link_to d.object.attachment_file_name, d.object.attachment
          %td
            = d.file_field :attachment, help: 'PDF Format', hide_label: true
          %td
            = d.link_to_remove 'Remove', class: 'btn btn-danger'
    %tfoot
      %tr
        %td{ colspan: 2 }
        %td.center
          = f.link_to_add 'Add Document', :documents, class: 'btn btn-primary', data: { target: '#documents' }
