/* General Purpose Styles */

.center {
  text-align: center;
}

.right {
  text-align: right;
}

.inline {
  display: inline-block;
}

/* Form Styles */

td {
  vertical-align: middle !important;
}

th {
  &.preview {
    width: 80px;
  }

  &.action-2 {
    width: 150px;
  }

  &.action-3 {
    width: 245px;
  }

  &.action-4 {
    width: 285px;
  }
}

li.hr {
  padding: 0;
}

.checkbox {
  padding-left: 0;
}

label.radio {
  font-weight: normal;
  padding-right: 20px;
}

div.radio {
  margin-top: -6px !important;
}

.select2-container {
  margin: 3px 0;
}

input[type="search"] {
  width: 350px !important;
  vertical-align: middle;
}

select[multiple] {
  width: 374px;
}

textarea {
  height: 300px !important;
}

input.datepicker {
  width: 179px;
  display: inline-block;
  margin: 0;
}

.rails-bootstrap-forms-date-select {
  select {
    width: 30%;
  }
}

.select2-container--bootstrap .select2-selection {
  border-radius: 0;
}

.nested-form-table {
  .form-group {
    width: 90%;
  }

  .col-sm-10,
  .col-sm-4 {
    padding: 0;
    width: 100%;
  }

  td {
    text-align: center;
  }

  .form-group {
    display: inline-block;
  }

  td:first-of-type {
    text-align: center;

    div {
      text-align: left;
    }
  }

  a.add_nested_fields {
    margin: 0 15px 15px;
  }

}

/* Larger Select 2 */

.select2-container {
  margin: 0;
}

.select2-container .select2-choice {
  height: 34px;
  border: 1px solid #ccc;
}

.select2-chosen {
  font-size: 14px;
  line-height: 32px;
}

.select2-container .select2-choice .select2-arrow b {
  background-position: 0px 4px;
}

.select2-dropdown-open .select2-choice .select2-arrow b {
  background-position: -17px 4px;
}

.select2-container-multi {
  width: 100% !important;
  border: 1px solid #ccc !important;
}

.select2-search-choice {
  margin: 6px 0 6px 6px !important;
}

.select2-dropdown--below {
  margin-top: -21px !important;
}

/* Smaller Table Buttons */

.table-striped .btn {
  line-height: 16px;
  font-size: 12px;
  padding: 5px 8px;
}

.table-striped td:last-of-type {
  text-align: center;
}
