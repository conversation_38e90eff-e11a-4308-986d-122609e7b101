$(window).bind('turbolinks:load', function() {
  initSortable();
})

function initSortable() {
  $("tbody.sortable").sortable({
      update: setIndexes,
      helper: fixHelper,
      handle: "i.glyphicon-move"
  }).disableSelection();

  setIndexes();
}

function setIndexes(event, ui){
  $('tbody.sortable tr').each(function(){
    $(this).find('input[type=text]').val($(this).index() + 1);
    $(this).find('span').text($(this).index() + 1);
  });
}

// Return a helper with preserved width of cells
var fixHelper = function(e, ui) {
  ui.children().each(function() {
      $(this).width($(this).width());
  });
  return ui;
};
