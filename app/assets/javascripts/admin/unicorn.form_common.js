$(window).bind('turbolinks:load', function() {
  initForms();
});

function initForms() {
	$('select').not(".skip-fancy").select2({ theme: 'bootstrap' });

  $('.update-status select').on("change", function(e) {
    $(this).parents("form").submit({ theme: 'bootstrap' });
  });

  $(document).on('nested:fieldAdded', function(e) {
    var link = $(e.currentTarget.activeElement);
    var target = $(link.data('target'));

    if (!link.data('limit')) {
      return;
    }

    if (target.find('.fields:visible').length >= link.data('limit')) {
      link.hide();
    }
  });

  $(document).on('nested:fieldRemoved', function(e) {
    var link = $(e.target).parent().siblings('a.add_nested_fields');
    var target = $(link.data('target'));

    if (!link.data('limit')) {
      return;
    }

    if (target.find('.fields:visible').length < link.data('limit')) {
      link.show();
    }
  });
}

$(document).on("nested:fieldAdded", "form", function(e){
  $('select').not(".skip-fancy").select2({ theme: 'bootstrap' });
});
