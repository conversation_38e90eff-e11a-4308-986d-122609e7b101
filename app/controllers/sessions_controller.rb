class SessionsController < ApplicationController
  def new
    redirect_to admin_root_path if current_user

    @user = User.new
  end

  def create
    @user = User.find_by(email: params[:user][:email]) if params[:user]

    unless verify_recaptcha(model: @user)
      flash[:error] = 'Invalid reCAPTCHA'
      return redirect_to new_session_path
    end

    if @user&.locked?
      flash.now[:error] = 'Account is locked'

      render :new
      return
    end

    if @user&.authenticate(params[:user][:password]) && @user&.role?(:administrator)
      sign_in(@user)

      if is_2fa_enabled?
        if @user.is_2fa_setup?
          redirect_to new_two_factor_auth_validate_path
        else
          redirect_to new_two_factor_auth_setup_path
        end
      else
        redirect_to admin_root_path
      end
    else
      error_msg = @user.errors.full_messages.to_sentence if @user && @user.errors.present?
      @user.login_failure!(request.remote_ip, request.user_agent) if @user
      @user = User.new(email: (params[:user][:email] rescue nil))
      flash.now[:error] = error_msg || 'Invalid admin username or password'

      render :new
    end
  end

  def destroy
    sign_out(current_user) if current_user

    redirect_to root_path
  end
end
