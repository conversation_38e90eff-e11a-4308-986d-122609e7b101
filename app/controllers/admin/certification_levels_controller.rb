class Admin::CertificationLevelsController < Admin::AdminController
  def index
    @certification_levels = Certification::Level.order(:position)
  end

  def reorder
    message = if Certification::Level.update(params[:certification_level].keys, params[:certification_level].values)
                'Ordering Updated'
              else
                'Unable To Update Ordering'
              end

    redirect_to admin_certification_levels_path, flash: { notice: message }
  end

  private

  def model
    ::Certification::Level
  end

  def allowed_params
    params.require(:certification_level).permit(:name,
                                                :attachment,
                                                :brief,
                                                :description,
                                                :default,
                                                :kind,
                                                questions_attributes: [:id,
                                                                       :question,
                                                                       :_destroy,
                                                                       answers_attributes: [:id,
                                                                                            :answer,
                                                                                            :correct,
                                                                                            :_destroy]])
  end

  def generate_breadcrumbs
    add_breadcrumb('Certification Levels', admin_certification_levels_path, 'signal')
    add_breadcrumb('Add Certification Level', '#', 'plus') if %w(new create).include?(params[:action])
    add_breadcrumb('Edit Certification Level', '#', 'edit') if %w(edit update).include?(params[:action])
  end
end
