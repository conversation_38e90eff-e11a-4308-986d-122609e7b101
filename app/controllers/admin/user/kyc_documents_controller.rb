class Admin::User::KycDocumentsController < Admin::AdminController
  include Admin::FetchResource

  before_action :check_state, only: [:new, :create]
  before_action :base64_documents, only: [:create]

  def index
    @kyc_documents = @user.kyc_documents
                          .order(created_at: :desc)
                          .page(params[:page])
  end

  def new; end

  def create
    @user.assign_attributes(allowed_params)

    if @user.kyc_regular_submitted!
      redirect_to admin_user_kyc_documents_path(@user), flash: { notice: 'Successfully submitted documents' }
    else
      render :new
    end
  end

  def refresh
    if @user.refresh_kyc_status!
      flash[:notice] = 'KYC Complete'
    else
      flash[:error] = 'KYC Failed or One or more documents still requires review'
    end

    redirect_to admin_user_kyc_documents_path(@user)
  end

  private

  def allowed_params
    params.require(:user).permit!
  end

  def set_title
    @title = 'KYC Documents'
  end

  def generate_breadcrumbs
    add_breadcrumb(@user.display_name, edit_admin_user_path(@user), 'user') if @user
    add_breadcrumb('KYC Documents', admin_user_kyc_documents_path(@user), 'file')
  end

  def check_state
    return if @user.kyc_light_is_complete? ||
              @user.kyc_regular_has_failed? ||
              @user.kyc_regular_is_required?

    redirect_to admin_user_kyc_documents_path(@user), flash: { error: 'User is not in correct state' }
  end

  def base64_documents
    return if params[:user].blank?

    [:identity_proof, :registration_proof, :articles_of_association, :shareholder_declaration].each do |param|
      next unless params[:user][param].present?

      bytes = File.open(params[:user][param].path, 'rb', &:read)
      params[:user][param] = Base64.strict_encode64(bytes)
    end
  end
end
