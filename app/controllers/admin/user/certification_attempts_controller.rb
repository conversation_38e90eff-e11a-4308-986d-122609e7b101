class Admin::User::CertificationAttemptsController < Admin::AdminController
  include Admin::FetchResource

  def index
    @certification_attempts = scope.includes(:certification_level, :user)

    respond_to do |format|
      format.csv do
        render csv: @certification_attempts,
               filename: "certification-attempts-#{Time.zone.now.to_formatted_s(:db).parameterize}"
      end

      format.html do
        @certification_attempts = @certification_attempts.order(created_at: :desc)
                                                         .page(params[:page])
      end
    end
  end

  private

  def scope
    if @user
      @user.certification_attempts
    else
      ::User::CertificationAttempt
    end
  end

  def set_title
    @title = 'Certification Attempts'
  end

  def generate_breadcrumbs
    add_breadcrumb(@user.display_name, edit_admin_user_path(@user), 'user') if @user
    add_breadcrumb('Certification Attempts', '#', 'signal')
  end
end
