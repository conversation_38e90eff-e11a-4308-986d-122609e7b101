class Admin::User::PortfolioItemsController < Admin::AdminController
  include Admin::FetchResource

  def index
    @portfolio_items = ::Share::Log.includes(:user, :property)
                                   .where('quantity > 0')
                                   .group(:user_id, :property_id)
                                   .order(:user_id)
                                   .select('share_logs.id,
                                      share_logs.user_id,
                                      share_logs.property_id,
                                      SUM(share_logs.quantity) AS total')
    respond_to do |format|
      format.html do
        @portfolio_items = @portfolio_items.page(params[:page])
      end
      format.csv {
        send_data Property.to_transposed_csv(@portfolio_items),
                  filename: "portfolio-items-export-#{ Time.zone.now.to_formatted_s(:db).parameterize}.csv"
      }
    end
  end

  private

  def set_title
    @title = 'Portfolio Items'
  end

  def generate_breadcrumbs
    add_breadcrumb('Portfolio Items', '#', 'briefcase')
  end
end
