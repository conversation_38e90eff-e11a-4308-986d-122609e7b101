class Admin::User::<PERSON>ginAttemptsController < Admin::AdminController
  include Admin::FetchResource

  def index
    @login_attempts = scope.includes(:user)
                           .order(created_at: :desc)
                           .page(params[:page])

    @geocoder = MaxMindDB.new(Rails.root.join('db', 'GeoLite2-City.mmdb'))
  end

  private

  def scope
    if @user
      @user.login_attempts
    else
      ::User::LoginAttempt
    end
  end

  def set_title
    @title = 'Login Attempts'
  end

  def generate_breadcrumbs
    add_breadcrumb(@user.display_name, edit_admin_user_path(@user), 'user') if @user
    add_breadcrumb('Login Attempts', '#', 'signal')
  end
end
