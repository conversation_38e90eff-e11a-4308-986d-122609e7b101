class Admin::User::QueuedActionsController < Admin::AdminController
  include Admin::FetchResource

  def index
    @queued_actions = scope.includes(:user, :source, :target)
                           .order(updated_at: :desc)
                           .page(params[:page])
  end

  private

  def scope
    if @user
      @user.queued_actions
    else
      ::User::QueuedAction
    end
  end

  def set_title
    @title = 'Queued Actions'
  end

  def generate_breadcrumbs
    add_breadcrumb(@user.display_name, edit_admin_user_path(@user), 'user') if @user
    add_breadcrumb('Queued Investments', '#', 'bookmark')
  end
end
