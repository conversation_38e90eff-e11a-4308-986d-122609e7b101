class Admin::ReferalsController < Admin::AdminController
  def index
    @start_date = params[:start_date].present? ? params[:start_date].to_date : 1.year.ago.to_date
    @end_date   = params[:end_date].present? ? params[:end_date].to_date : Time.zone.now.to_date

    @referrals = User.select('referer_host, count(referer_host) as total')
                     .group(:referer_host)
                     .order('total DESC')
                     .where('created_at between ? and ?', @start_date, @end_date.end_of_day)
                     .where.not(referer_host: nil)
                     .page(params[:page])
  end

  private

  def set_title
    @title = 'Referrals'
  end
end
