class Admin::MandatesController < Admin::AdminController
  include Admin::FetchResource

  def index
    @q = scope.includes(:user).ransack(params[:q])
    @mandates = @q.result(distinct: true).order(created_at: :desc)

    respond_to do |format|
      format.html do
        @mandates = @mandates.page(params[:page])
      end

      format.csv do
        @mandates = @mandates.includes(user: :certification_level)

        render csv: @mandates, filename: "mandate-export-#{Time.zone.now.to_formatted_s(:db).parameterize}"
      end
    end
  end

  private

  def scope
    if @user
      @user.mandates
    else
      ::Mandate
    end
  end

  def set_title
    @title = if @user
               "Direct Debits (#{@user.display_name})"
             else
               'Direct Debits'
             end
  end

  def generate_breadcrumbs
    add_breadcrumb(@user.display_name, edit_admin_user_path(@user), 'user') if @user
    add_breadcrumb('Direct Debits', admin_mandates_path, 'list-alt')
  end
end
