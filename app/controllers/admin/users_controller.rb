class Admin::UsersController < Admin::AdminController
  def index
    @q = User.ransack(params[:q])
    @users = @q.result(distinct: true).order(:first_name, :last_name)
    if params[:export_csv]
      render csv: @users,
             filename: "user-export-#{Time.zone.now.to_formatted_s(:db).parameterize}"
    else
      @users = @users.page(params[:page])
    end
  end

  def edit
    @user = fetch_user
  end

  def update
    @user = fetch_user

    if @user.update(allowed_params)
      redirect_to_stored_location(admin_users_path,
                                  flash: { notice: 'User was successfully updated.' })
    else
      render :edit
    end
  end

  def wallet_report
    UserWalletReportJob.perform_later(current_user.id)
    redirect_to admin_users_path, flash: { notice: 'User wallet report has been scheduled' }
  end

  def fetch_kyc_documents
    user = User.find(params[:id])
    SyncKycDocumentsJob.perform_later(user.id)
    redirect_to edit_admin_user_path(user), notice: 'KYC documents fetch job has been enqueued.'
  end

  private

  def fetch_user
    User.find(params[:id])
  end

  def allowed_params
    address_params = [:id, :address_number, :address_1, :address_2, :city, :country,
                      :date_of_birth, :first_name, :kind, :last_name, :post_code, :_destroy]

    params.require(:user).permit(:business_name, :business_number, :call_me, :certification_level_id,
                                 :country_of_residence, :date_of_birth, :email, :employment_status,
                                 :experience, :first_name, :income_range, :last_name, :legal_type,
                                 :marketing, :middle_name, :nationality, :occupation, :phone_number,
                                 :planned_investment, :title, :approved, :payout_threshold_approved,
                                 role_ids: [],
                                 address_attributes: address_params,
                                 headquarters_attributes: address_params,
                                 directors_attributes: address_params,
                                 shareholders_attributes: address_params)
  end

  def generate_breadcrumbs
    add_breadcrumb('Users', admin_users_path, 'user')
    add_breadcrumb('Add User', '#', 'plus') if %w[new create].include?(params[:action])
    add_breadcrumb('Edit User', '#', 'edit') if %w[edit update].include?(params[:action])
    add_breadcrumb('Shares', '#', 'book') if params[:action] == 'shares'
  end
end
