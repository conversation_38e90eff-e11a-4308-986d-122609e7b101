class Admin::PropertyLoansController < Admin::AdminController
  include Admin::Property::SharedActions

  private

  def set_title
    @title = if @object&.placeholder?
               'Loans (Placeholder)'
             else
               'Loans'
             end
  end

  def allowed_params
    params.require(:property_loan).permit(:acknowledgement_pdf,
                                          :annualised_return,
                                          :description,
                                          :estimated_completion_date,
                                          :finance_in_pounds,
                                          :finance_label,
                                          :funded,
                                          :gdv_in_pounds,
                                          :guaranteed_yield,
                                          :investment_case_and_risk,
                                          :name,
                                          :placeholder,
                                          :profit_in_pounds,
                                          :property_amount_in_pounds,
                                          :property_amount_label,
                                          :property_fee_legal_and_professional_in_pounds,
                                          :property_fee_legal_and_professional_label,
                                          :share_count,
                                          :site_value_in_pounds,
                                          :site_value_label,
                                          :spv_name,
                                          :term,
                                          :thumbnail_label,
                                          :visible,
                                          :address_1, :address_2, :city, :postcode,
                                          documents_attributes: [:id, :attachment, :_destroy],
                                          floorplans_attributes: [:id, :attachment, :_destroy],
                                          legal_documents_attributes: [:id, :attachment, :title, :_destroy],
                                          photos_attributes: [:id, :attachment, :_destroy],
                                          tag_ids: [],
                                          certification_level_ids: [])
  end

  def generate_breadcrumbs
    add_breadcrumb('Loans', admin_property_loans_path, 'piggy-bank')
    add_breadcrumb('Add Loan', '#', 'plus') if %w[new create].include?(params[:action])
    add_breadcrumb('Edit Loan', '#', 'edit') if %w[edit update].include?(params[:action])
  end

  def model
    ::Property::Loan
  end
end
