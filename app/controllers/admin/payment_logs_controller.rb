class Admin::PaymentLogsController < Admin::AdminController
  include Admin::FetchResource

  def index
    @q = scope.includes(:user).ransack(params[:q])
    @payment_logs = @q.result(distinct: true).order(updated_at: :desc)

    respond_to do |format|
      format.html do
        @payment_logs = @payment_logs.page(params[:page])
      end

      format.csv do
        @payment_logs = @payment_logs.includes(user: :certification_level)

        render csv: @payment_logs, filename: "payment-logs-export-#{Time.zone.now.to_formatted_s(:db).parameterize}"
      end
    end
  end

  private

  def scope
    if @user
      @user.payment_logs
    else
      ::PaymentLog
    end
  end

  def set_title
    @title = if @user
               "Payment Logs (#{@user.display_name})"
             else
               'Payment Logs'
             end
  end

  def generate_breadcrumbs
    add_breadcrumb(@user.display_name, edit_admin_user_path(@user), 'user') if @user
    add_breadcrumb('Payment Logs', admin_payment_logs_path, 'piggy-bank')
  end
end
