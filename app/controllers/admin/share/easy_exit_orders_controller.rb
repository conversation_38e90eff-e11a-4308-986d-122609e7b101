class Admin::Share::EasyExitOrdersController < Admin::AdminController
  def index
    @q = ::Share::EasyExitOrder.includes(:property, :user).ransack(params[:q])
    @share_easy_exit_orders = @q.result(distinct: true).order(created_at: :desc).page(params[:page])
  end

  def show
    @share_easy_exit_order = fetch_order
  end

  def accept
    @share_easy_exit_order = fetch_order
    @share_easy_exit_order.activate!

    redirect_to admin_share_easy_exit_orders_path, flash: { notice: 'Easy exit will be processed shortly' }
  end

  def reject
    @share_easy_exit_order = fetch_order
    @share_easy_exit_order.reject!

    redirect_to admin_share_easy_exit_orders_path, flash: { notice: 'Easy exit will be rejected shortly' }
  end

  private

  def fetch_order
    ::Share::EasyExitOrder.find(params[:id])
  end
end
