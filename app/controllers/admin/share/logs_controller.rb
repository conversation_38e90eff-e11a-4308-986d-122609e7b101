class Admin::Share::LogsController < Admin::AdminController
  include Admin::FetchResource

  def index
    @share_logs = scope.includes(:property, :user).order(updated_at: :desc)

    respond_to do |format|
      format.html do
        @grouped_by_user = grouped_by_user if @property
        @grouped_by_property = grouped_by_property if @user
        @share_logs = @share_logs.page(params[:page])
      end

      format.csv do
        if @user
          @share_logs = grouped_by_property
          style = :portfolio_item
        elsif @property
          @share_logs = grouped_by_user
          style = :portfolio_item
        else
          @share_logs = @share_logs.includes(user: :certification_level)
          style = :default
        end

        render csv: @share_logs, style: style, filename: "share-logs-export-#{Time.zone.now.to_formatted_s(:db).parameterize}"
      end
    end
  end

  private

  def grouped_by_user
    ::Share::Log.select('sum(share_logs.quantity) as total,
                         max(share_logs.id),
                         share_logs.property_id,
                         share_logs.user_id')
                .includes(:user)
                .where(property_id: @property.id)
                .group(:user_id)
                .sort { |a, b| b.total <=> a.total }
  end

  def grouped_by_property
    ::Share::Log.select('sum(share_logs.quantity) as total,
                         max(share_logs.id),
                         share_logs.property_id,
                         share_logs.user_id')
                .includes(:property)
                .where(user_id: @user.id)
                .group(:property_id)
                .sort { |a, b| b.total <=> a.total }
  end

  def scope
    if @user
      @user.share_logs
    elsif @property
      @property.share_logs
    else
      ::Share::Log.where('quantity > 0')
    end
  end

  def set_title
    @title = if @user
               "Share Logs (#{@user.display_name})"
             elsif @property
               "Share Logs (#{@property.name})"
             else
               'Share Logs'
             end
  end

  def generate_breadcrumbs
    add_breadcrumb(@user.display_name, edit_admin_user_path(@user), 'user') if @user
    add_breadcrumb(@property.name, url_for([:edit, :admin, @property]), 'home') if @property
    add_breadcrumb('Share Logs', admin_share_logs_path, 'book')
  end
end
