class Admin::Share::OrdersController < Admin::AdminController
  include Admin::FetchResource

  def index
    @q = scope.includes(:property, :user).ransack(params[:q])
    @share_orders = @q.result(distinct: true).order(created_at: :desc)

    respond_to do |format|
      format.html do
        @share_orders = @share_orders.page(params[:page])
      end

      format.csv do
        @share_orders = @share_orders.includes(user: :certification_level)

        render csv: @share_orders, filename: "share-orders-export-#{Time.zone.now.to_formatted_s(:db).parameterize}"
      end
    end
  end

  def cancel
    @order = Share::Order.find(params[:id])

    if @order.queue_cancellation!
      flash[:notice] = 'Order has been queued for cancellation'
    else
      flash[:error] = 'Order can not be cancelled'
    end

    redirect_back fallback_location: admin_share_orders_path
  end

  private

  def scope
    if @user
      @user.share_orders
    elsif @property
      @property.share_orders
    else
      ::Share::Order
    end
  end

  def set_title
    @title = if @user
               "Share Orders (#{@user.display_name})"
             elsif @property
               "Share Orders (#{@property.name})"
             else
               'Share Orders'
             end
  end

  def generate_breadcrumbs
    add_breadcrumb(@user.display_name, edit_admin_user_path(@user), 'user') if @user
    add_breadcrumb(@property.name, url_for([:edit, :admin, @property]), 'home') if @property
    add_breadcrumb('Share Orders', admin_share_orders_path, 'book')
  end
end
