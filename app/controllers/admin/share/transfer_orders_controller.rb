class Admin::Share::TransferOrdersController < Admin::AdminController
  def index
    @share_transfer_orders = ::Share::TransferOrder.includes(:property, :user, :creator, :source_user)
                                                   .order(updated_at: :desc)
                                                   .page(params[:page])
  end

  def new
    @share_transfer_order = ::Share::TransferOrder.new(source_user: Mango::LegalUser.root)
  end

  def create
    @share_transfer_order = ::Share::TransferOrder.new(allowed_params)
    @share_transfer_order.creator = current_user

    if @share_transfer_order.save
      redirect_to_stored_location(admin_share_transfer_orders_path,
                                  flash: { notice: 'Transfer was successfully created.' })
    else
      render :new
    end
  end

  def details
    user = User.find(params[:user_id])
    property = Property.find(params[:property_id])

    if user && property
      render json: {
        name: user.display_name,
        email: user.email,
        shares_available: user.shares_available_for_property(property)
      }
    else
      render json: {}
    end
  end

  private

  def object_name
    'Share Transfer'
  end

  def allowed_params
    params.require(:share_transfer_order).permit(:description,
                                                 :property_id,
                                                 :quantity,
                                                 :quantity_confirmation,
                                                 :source_user_id,
                                                 :user_id)
  end

  def generate_breadcrumbs
    add_breadcrumb('Share Transfers', admin_share_transfer_orders_path, 'book')
  end
end
