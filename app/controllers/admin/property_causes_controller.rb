class Admin::PropertyCausesController < Admin::AdminController
  include Admin::Property::SharedActions

  private

  def set_title
    @title = if @object&.placeholder?
               'Causes (Placeholder)'
             else
               'Causes'
             end
  end

  def allowed_params
    params.require(:property_cause).permit(:description,
                                           :funded,
                                           :name,
                                           :placeholder,
                                           :property_amount_in_pounds,
                                           :share_count,
                                           :site_value_in_pounds,
                                           :thumbnail_label,
                                           :visible,
                                           documents_attributes: [:id, :attachment, :_destroy],
                                           photos_attributes: [:id, :attachment, :_destroy],
                                           tag_ids: [],
                                           certification_level_ids: [])
  end

  def generate_breadcrumbs
    add_breadcrumb('Causes', admin_property_developments_path, 'heart')
    add_breadcrumb('Add Development', '#', 'plus') if %w[new create].include?(params[:action])
    add_breadcrumb('Edit Development', '#', 'edit') if %w[edit update].include?(params[:action])
  end

  def model
    ::Property::Cause
  end
end
