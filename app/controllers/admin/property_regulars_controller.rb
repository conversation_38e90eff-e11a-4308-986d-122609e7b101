class Admin::PropertyRegularsController < Admin::AdminController
  include Admin::Property::SharedActions

  private

  def set_title
    @title = if @object&.placeholder?
               'Property (Placeholder)'
             else
               'Property'
             end
  end

  def allowed_params
    params.require(:property_regular).permit(:description,
                                             :easy_exit,
                                             :funded,
                                             :guaranteed_yield,
                                             :hpi,
                                             :hpi_area,
                                             :investment_case_and_risk,
                                             :name,
                                             :placeholder,
                                             :property_amount_in_pounds,
                                             :property_fee_deferred_tax_in_pounds,
                                             :property_fee_legal_and_professional_in_pounds,
                                             :property_fee_pre_let_expenses_in_pounds,
                                             :property_fee_repairs_provision_in_pounds,
                                             :property_fee_stamp_duty_in_pounds,
                                             :rent_amount_in_pounds,
                                             :rental_fee_allowance_for_voids_in_pounds,
                                             :rental_fee_corporation_tax_in_pounds,
                                             :rental_fee_deferred_fees_in_pounds,
                                             :rental_fee_insurance_in_pounds,
                                             :rental_fee_maintenance_allowance_in_pounds,
                                             :rental_fee_management_in_pounds,
                                             :rental_fee_spv_charge_in_pounds,
                                             :share_count,
                                             :spv_name,
                                             :thumbnail_label,
                                             :visible,
                                             :address_1, :address_2, :city, :postcode,
                                             documents_attributes: [:id, :attachment, :_destroy],
                                             floorplans_attributes: [:id, :attachment, :_destroy],
                                             legal_documents_attributes: [:id, :attachment, :title, :_destroy],
                                             photos_attributes: [:id, :attachment, :_destroy],
                                             tag_ids: [],
                                             certification_level_ids: [])
  end

  def generate_breadcrumbs
    add_breadcrumb('Properties', admin_property_regulars_path, 'home')
    add_breadcrumb('Add Property', '#', 'plus') if %w[new create].include?(params[:action])
    add_breadcrumb('Edit Property', '#', 'edit') if %w[edit update].include?(params[:action])
  end

  def model
    ::Property::Regular
  end
end
