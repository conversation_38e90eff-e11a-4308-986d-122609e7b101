class Admin::AdminController < ApplicationController
  layout 'admin'

  before_action :check_admin
  before_action :check_2fa
  before_action :store_location, only: [:index]

  include Admin::Breadcrumbs
  include Admin::ResourceController

  private

  def check_admin
    return if current_user&.role?(:administrator)

    redirect_to root_path, flash: { error: 'You do not have permission to access there' }
  end

  def check_2fa
    return true unless is_2fa_enabled?

    redirect_to new_two_factor_auth_validate_path unless authenticated_2fa?
  end
end
