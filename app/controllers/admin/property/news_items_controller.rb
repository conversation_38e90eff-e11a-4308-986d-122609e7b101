class Admin::Property::NewsItemsController < Admin::AdminController
  prepend_before_action :find_property
  before_action :find_object, except: [:index, :new, :create]

  def index
    @news_items = @property.news_items
                           .order(date: :desc)
                           .page(params[:page])
  end

  def new
    @object = model.new(user: current_user, property: @property)
  end

  def create
    @object = model.new(allowed_params.merge(user: current_user, property: @property))

    if @object.save
      redirect_to_stored_location(admin_property_news_items_path(@property),
                                  flash: { notice: "#{object_name.titleize} was successfully created." })
    else
      render :new
    end
  end

  def edit; end

  def update
    if @object.update(allowed_params)
      redirect_to_stored_location(admin_property_news_items_path(@property),
                                  flash: { notice: "#{object_name.titleize} was successfully updated." })
    else
      render :edit
    end
  end

  private

  def model
    ::Property::NewsItem
  end

  def find_property
    @property = ::Property.find(params[:property_id])
  end

  def find_object
    @object = @property.news_items.find(params[:id])
  end

  def allowed_params
    params.require(:property_news_item)
          .permit(:date, :content, :title)
  end

  def generate_breadcrumbs
    add_breadcrumb(@property.name, url_for([:edit, :admin, @property]), 'home')
    add_breadcrumb('News Items', admin_property_news_items_path(@property), 'list-alt')
    add_breadcrumb('Add News', '#', 'plus') if %w[new create].include?(params[:action])
    add_breadcrumb('Edit News', '#', 'edit') if %w[edit update].include?(params[:action])
  end
end
