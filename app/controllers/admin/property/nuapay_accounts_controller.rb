class Admin::Property::NuapayAccountsController < Admin::AdminController
  def index
    @nuapay_accounts = ::RemoteBankAccount.includes(:bankable)
    respond_to do |format|
      format.html do
        @nuapay_accounts = @nuapay_accounts.page(params[:page])
      end
    end
  end

  private

  def generate_breadcrumbs
    add_breadcrumb('Nuapay Account', admin_property_nuapay_accounts_path, 'piggy-bank')
  end
end
