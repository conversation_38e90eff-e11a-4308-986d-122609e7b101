class Admin::Property::TagsController < Admin::AdminController
  def index
    @property_tags = ::Property::Tag.order(:name)
  end

  private

  def model
    ::Property::Tag
  end

  def allowed_params
    params.require(:property_tag)
          .permit(:name, :attachment)
  end

  def generate_breadcrumbs
    add_breadcrumb('Tags', admin_property_tags_path, 'tags')
    add_breadcrumb('Add Tag', '#', 'plus') if %w[new create].include?(params[:action])
    add_breadcrumb('Edit Tag', '#', 'edit') if %w[edit update].include?(params[:action])
  end
end
