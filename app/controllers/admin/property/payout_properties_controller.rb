class Admin::Property::PayoutPropertiesController < Admin::AdminController
  before_action :find_object, except: [:index, :new, :create]

  def index
    # @payout_properties = model.includes(:property, :user)
    #                           .order(created_at: :desc)
    @q = model.includes(:property, :user).ransack(params[:q])
    @payout_properties = @q.result(distinct: true).order(created_at: :desc)
  end

  def new
    @object.build_default_fees

    render 'admin/resources/new'
  end

  def create
    @object = model.new(allowed_params)

    if @object.save
      redirect_to confirm_totals_admin_property_payout_property_path(@object)
    else
      render 'admin/resources/new'
    end
  end

  def allocate_funds
    @object.allocate_funds!
    @object.transfer_fees!

    redirect_to confirm_dividends_admin_property_payout_property_path(@object)
  rescue => e
    msg = @object.errors&.full_messages&.to_sentence || e.message
    redirect_back fallback_location: admin_property_payout_properties_path, flash: { error: msg }
  end

  def distribute_dividends
    @object.distribute_dividends!

    redirect_to admin_property_payout_property_path(@object, anchor: 'distribution'), flash: { notice: 'Dividends are now being distributed' }
  rescue => e
    redirect_back fallback_location: admin_property_payout_properties_path, flash: { error: e.message }
  end

  private

  def set_title
    @title = 'Property Payouts'
  end

  def object_name
    'Payout'
  end

  def model
    ::Property::PayoutProperty
  end

  def allowed_params
    params.require(:property_payout_property)
          .permit(:user_id,
                  :property_id,
                  :amount_in_pounds)
  end

  def generate_breadcrumbs
    add_breadcrumb('Property Payouts', admin_property_payout_properties_path, 'book')
    add_breadcrumb('Payout Development', '#', 'plus') if %w[new create].include?(params[:action])
    add_breadcrumb('Confirm Totals', '#', 'piggy-bank') if params[:action] == 'confirm_totals'
    add_breadcrumb('Confirm Dividends', '#', 'piggy-bank') if params[:action] == 'confirm_dividends'
  end
end
