class Admin::Property::PayoutRentsController < Admin::AdminController
  before_action :find_object, except: [:index, :new, :create]

  def index
    @q = model.includes(:property, :user).ransack(params[:q])
    @payout_rents = @q.result(distinct: true).order(created_at: :desc)
  end

  def new
    @object = find_object if params[:id]
    @object.end_date = Date.today.prev_month.end_of_month

    render 'admin/resources/new'
  end

  def create
    @object = model.new(allowed_params)

    if @object.save
      redirect_to confirm_totals_admin_property_payout_rent_path(@object)
    else
      render 'admin/resources/new'
    end
  end

  def allocate_funds
    @object.allocate_funds!
    @object.transfer_fees!

    redirect_to confirm_dividends_admin_property_payout_rent_path(@object)
  rescue => e
    redirect_back fallback_location: admin_property_payout_rents_path, flash: { error: e.message }
  end

  def distribute_dividends
    @object.distribute_dividends!

    redirect_to admin_property_payout_rent_path(@object, anchor: 'distribution'), flash: { notice: 'Dividends are now being distributed' }
  rescue => e
    redirect_back fallback_location: admin_property_payout_rents_path, flash: { error: e.message }
  end

  def show
    @object = model.find(params[:id])
  end

  def last_month_amount
    property_id = params[:property_id]
    start_of_last_month = Date.today.prev_month.beginning_of_month
    end_of_last_month = Date.today.prev_month.end_of_month

    last_record = Property::PayoutRent.where(property_id: property_id)
                           .where('start_date >= ? AND end_date <= ?', start_of_last_month, end_of_last_month)
                           .order(created_at: :desc)
                           .first
    if last_record
      render json: { amount: last_record.amount.to_f / 100 }
    else
      render json: { amount: '0.0' }
    end
  end

  def update
    create
  end

  private

  def model
    ::Property::PayoutRent
  end

  def set_title
    @title = 'Rent/Interest Payments'
  end

  def allowed_params
    params.require(:property_payout_rent)
          .permit(:user_id,
                  :property_id,
                  :amount_in_pounds,
                  :start_date,
                  :end_date,
                  fees_attributes: [:description, :amount_in_pounds, :_destroy])
  end

  def generate_breadcrumbs
    add_breadcrumb('Rent/Interest Payments', admin_property_payout_rents_path, 'book')
    add_breadcrumb('Add Payment', '#', 'plus') if %w[new create].include?(params[:action])
    add_breadcrumb('Confirm Totals', '#', 'piggy-bank') if params[:action] == 'confirm_totals'
    add_breadcrumb('Confirm Dividends', '#', 'piggy-bank') if params[:action] == 'confirm_dividends'
  end
end
