class Admin::Property::DividendsController < Admin::AdminController
  def index
    @property_dividends = ::Property::Dividend.includes(:property, :user)
                                              .order(created_at: :desc)

    respond_to do |format|
      format.html do
        @property_dividends = @property_dividends.page(params[:page])
      end

      format.csv do
        @property_dividends = @property_dividends.includes(user: :certification_level)

        render csv: @property_dividends, filename: "property-dividends-export-#{Time.zone.now.to_formatted_s(:db).parameterize}"
      end
    end
  end

  private

  def generate_breadcrumbs
    add_breadcrumb('Dividends', admin_property_dividends_path, 'piggy-bank')
  end
end
