class Admin::NotificationsController < Admin::AdminController
  before_action :set_noticed_event , only: [:seen, :destroy]

  def index
    @notifications = Noticed::Event.newest_first.where(type: params[:notifier])
                                                .page(params[:page])
  end

  def seen
    @event.update(seen_at: Time.now)
    redirect_to_index
  end

  def destroy
    @event.destroy
    redirect_to_index
  end

  private

  def set_noticed_event
    @event = Noticed::Event.find(params[:id])
  end

  def redirect_to_index
    redirect_to admin_notifications_path(notifier: params[:notifier])
  end
end
