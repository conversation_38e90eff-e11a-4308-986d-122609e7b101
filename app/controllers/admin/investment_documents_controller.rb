class Admin::InvestmentDocumentsController < Admin::AdminController
  def index
    @investment_documents = InvestmentDocument.order(:name).page(params[:page])
  end

  private

  def allowed_params
    params.require(:investment_document).permit(:name,
                                                :description,
                                                :attachment)
  end

  def generate_breadcrumbs
    add_breadcrumb('Investment Documents', admin_investment_documents_path, 'home')
    add_breadcrumb('Add Investment Document', '#', 'plus') if %w[new create].include?(params[:action])
    add_breadcrumb('Edit Investment Document', '#', 'edit') if %w[edit update].include?(params[:action])
  end
end
