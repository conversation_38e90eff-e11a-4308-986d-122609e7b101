require 'net/https'
class ApplicationController < ActionController::Base
  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead.
  protect_from_forgery with: :exception
  rescue_from ActionView::MissingTemplate, with: :handle_missing_template
  rescue_from ActionController::UnknownFormat, with: :handle_missing_template

  include ExceptionHandling
  include SessionUtils

  def verify_recaptcha?(token, recaptcha_action)
    secret_key = Rails.application.secrets.recaptcha[:secret_key]
    uri = URI.parse("https://www.google.com/recaptcha/api/siteverify?secret=#{secret_key}&response=#{token}")
    response = Net::HTTP.get_response(uri)
    json = JSON.parse(response.body)
    json['success'] && json['score'] >= Rails.application.secrets.recaptcha[:minimum_score] && json['action'] == recaptcha_action
  rescue=>e
    false
  end

  def handle_missing_template(exception)
    head :not_found
  end
end
