class TwoFactorAuth::SetupController < ApplicationController
  before_action :check_user

  def new
    @user = current_user
  end

  def create
    @user = current_user

    if @user.register_2fa(allowed_params[:authy_code], allowed_params[:authy_number])
      redirect_to new_two_factor_auth_validate_path
    else
      render :new
    end
  end

  private

  def allowed_params
    params.require(:user).permit(:authy_number, :authy_code)
  end

  def check_user
    unless current_user
      redirect_to new_session_path
      return
    end

    redirect_to new_two_factor_auth_validate_path if current_user.is_2fa_setup?
  end
end
