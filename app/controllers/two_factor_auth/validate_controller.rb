class TwoFactorAuth::ValidateController < ApplicationController
  before_action :check_user

  def new
    @user = current_user
    @user.request_2fa
  end

  def create
    @user = current_user

    if @user.verify_2fa(allowed_params[:authy_token])
      sign_in_2fa(@user)

      redirect_to admin_root_path
    else
      render :new
    end
  end

  private

  def allowed_params
    params.require(:user).permit(:authy_token)
  end

  def check_user
    unless current_user
      redirect_to new_session_path
      return
    end

    redirect_to new_two_factor_auth_setup_path unless current_user.is_2fa_setup?
  end
end
