module SessionUtils
  extend ActiveSupport::Concern

  included do
    helper_method :current_user
    helper_method :is_2fa_enabled?
    helper_method :authenticated_2fa?
  end

  def current_user
    # If they have no session or session key or cookie then return nil
    return nil unless session[:user_id]

    # Find a user aslong as current user doesn't exist
    @current_user ||= User.authenticate_from_token(session[:user_id], session[:authentication_token])

    # If we didnt find the user return nil
    return nil unless @current_user

    # Return the user
    @current_user
  end

  def authenticated_2fa?
    return false unless current_user && session[:two_factor_authentication_token].present?

    current_user.two_factor_authentication_token == session[:two_factor_authentication_token]
  end

  def sign_in(user)
    user.ensure_authentication_token!
    user.login_success!(request.remote_ip, request.user_agent)

    session[:user_id] = user.id
    session[:authentication_token] = user.authentication_token
    @current_user = user
  end

  def sign_in_2fa(user)
    user.ensure_2fa_token!
    session[:two_factor_authentication_token] = user.two_factor_authentication_token
    @current_user = user
  end

  def sign_out(user)
    user.update(authentication_token: nil)
    session.delete(:user_id)
    session.delete(:authentication_token)
    session.delete(:two_factor_authentication_token)
  end

  def store_location
    return unless params[:format].blank? || (params[:format].present? && params[:format] == 'html')

    session[:return_to] = request.fullpath
  end

  def redirect_to_stored_location(default, args = {})
    if session[:return_to].present?
      path = session[:return_to]
      session[:return_to] = nil
      redirect_to path, args
    else
      redirect_to default, args
    end
  end

  def is_2fa_enabled?
    !!Rails.application.secrets.authy[:enabled]
  end
end
