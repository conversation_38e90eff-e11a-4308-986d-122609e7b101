module Admin
  module Bread<PERSON><PERSON><PERSON>
    extend ActiveSupport::Concern

    included do
      before_action :init_breadcrumbs
      helper_method :add_breadcrumb
    end

    # Set the page title and heading title
    def set_title
      @title = params[:controller].split('/').last.titleize
    end

    # Build the default breadcrumbs
    def init_breadcrumbs
      @breadcrumbs = []
      add_breadcrumb('Dashboard', admin_root_path, 'home')
    end

    def add_breadcrumb(text, link = '#', icon = nil)
      @breadcrumbs << if icon
                        "<a href='#{link}'><i class='glyphicon glyphicon-#{icon}'></i> #{text}</a>"
                      else
                        "<a href='#{link}'>#{text}</a>"
                      end
    end

    def generate_breadcrumbs
      # This is a blank method that should be overidden in each admin controller
    end

    # Before we render the page set the title and build the breadcrumbs
    def render(*args)
      set_title
      generate_breadcrumbs
      super
    end
  end
end
