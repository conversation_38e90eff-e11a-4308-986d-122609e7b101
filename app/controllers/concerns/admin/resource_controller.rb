module Admin
  module ResourceC<PERSON>roller
    extend ActiveSupport::Concern

    # Implements basic CRUD actions

    included do
      before_action :set_model
      before_action :new_object, only: [:index, :new]
      before_action :find_object, only: [:edit, :update, :destroy]

      helper_method :pluralized_object_path
      helper_method :index_path
      helper_method :object_name
    end

    def new
      render 'admin/resources/new'
    end

    def create
      @object = model.new(allowed_params)

      if @object.save
        redirect_to_stored_location(pluralized_object_path,
                                    flash: { notice: "#{object_name.titleize} was successfully created." })
      else
        render 'admin/resources/new'
      end
    end

    def edit
      render 'admin/resources/edit'
    end

    def update
      if @object.update(allowed_params)
        redirect_to_stored_location(pluralized_object_path,
                                    flash: { notice: "#{object_name.titleize} was successfully updated." })
      else
        render 'admin/resources/edit'
      end
    end

    def destroy
      @object.destroy
      render 'admin/resources/destroy'
    end

    def set_model
      @model ||= self.class.to_s.split('::').last.gsub('Controller', '').singularize.constantize rescue nil
    end
    alias model set_model

    def new_object
      @object = model.new rescue nil
    end

    def find_object
      @object = model.find(params[:id]) rescue nil
    end

    def pluralized_object_path
      "/admin/#{object_name.underscore.pluralize.downcase}"
    end
    alias index_path pluralized_object_path

    def object_name
      model.model_name.to_s.gsub('::', '')
    end
  end
end
