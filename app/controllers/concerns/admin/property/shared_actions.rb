module Admin
  module Property
    module SharedActions
      extend ActiveSupport::Concern

      included do
        before_action :find_object, only: [:edit, :update, :destroy, :upgrade]
      end

      def index
        @q = model.ransack(params[:q])
        @properties = @q.result(distinct: true).order(visible: :desc, name: :asc).page(params[:page])
      end

      def new
        @object = model.new
        @object.placeholder = params[:placeholder].present?

        render 'admin/resources/new'
      end

      def upgrade
        if @object.placeholder?
          if @object.upgrade_from_placeholder!
            flash[:notice] = t('properties.upgraded')
          else
            flash[:error] = t('properties.upgrade_failed')
          end
        else
          flash[:error] = t('properties.not_placeholder')
        end

        redirect_to [:admin, @object.class]
      end
    end
  end
end
