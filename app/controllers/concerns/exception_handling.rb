module ExceptionHandling
  extend ActiveSupport::Concern

  included do
    rescue_from ActionController::ParameterMissing, with: :handle_missing_params
    rescue_from ActionController::InvalidAuthenticityToken, with: :handle_invalid_token
  end

  def handle_missing_params
    redirect_to root_path, flash: { error: 'Parameters Missing' }
  end

  def handle_invalid_token
    redirect_to root_path, flash: { error: 'Your session has expired. Please try logging in again.' }
  end
end
