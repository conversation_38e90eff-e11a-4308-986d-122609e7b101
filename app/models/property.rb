require_dependency UownCore::Engine.root.join('app', 'models', 'property')

class Property < ActiveRecord::Base
  def self.to_transposed_csv(portfolio_items)
    properties = visible

    CSV.generate(headers: true) do |csv|
      # Header row
      # User ID, User Email, User Name, User Phone, Property 1, Property 2, Property 3, ...
      csv << ['User ID', 'User Email', 'User Name', 'User Phone',] + properties.pluck(:name)

      grouped_portfolio_items = portfolio_items.group_by(&:user_id)

      grouped_portfolio_items.each do |grouped_portfolio_item|
        portfolio_item = grouped_portfolio_item.last.first
        csv << [portfolio_item.user_id, portfolio_item.user.email, portfolio_item.user.display_name,
                portfolio_item.user.phone_number] + properties.map { |property|
                                                      grouped_portfolio_item.last.find { |item|
                                                        item.property_id == property.id
                                                      }.try(:total)
                                                    }
      end
    end
  end

  def self.ransackable_attributes(auth_object = nil)
    %w(name)
  end
  def self.ransackable_associations(auth_object = nil)
    ["payout_properties", "payout_rents"]
  end

  def self.live
    Property.where(placeholder: false).select { |property| property.available_shares > 0 }
  end
end
