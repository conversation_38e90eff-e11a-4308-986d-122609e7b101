module Auth::TwoFactorAuthentication
  extend ActiveSupport::Concern

  COUNTRY_CODE = '44'.freeze
  PK_COUNTRY_CODE = '92'.freeze

  included do
    attr_accessor :authy_token
  end

  def ensure_2fa_token!
    self.two_factor_authentication_token = generate_token(:two_factor_authentication_token)

    save(validate: false)
  end

  def is_2fa_setup?
    authy_id.present?
  end

  def register_2fa(code, number)
    authy = Authy::API.register_user(email: email,
                                     cellphone: number,
                                     country_code: country_code(code))

    if authy.ok?
      update_columns(authy_number: number, authy_id: authy.id)
    else
      errors.add(:authy_number, authy.errors['cellphone']) if authy.errors.key?('cellphone')
      false
    end
  end

  def request_2fa
    return false unless is_2fa_setup?

    response = Authy::API.request_sms(id: authy_id)

    if response.ok?
      true
    else
      errors.add(:authy_token, response.errors['message'])
    end
  end

  def verify_2fa(token)
    response = Authy::API.verify(id: authy_id,
                                 token: token)

    if response.ok?
      true
    else
      errors.add(:authy_token, 'is invalid')
      false
    end
  end

  def country_code(code)
    code == '+92'? PK_COUNTRY_CODE : COUNTRY_CODE
  end
end
