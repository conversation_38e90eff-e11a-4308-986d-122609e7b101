require_dependency UownCore::Engine.root.join('app', 'models', 'user')

class User < ActiveRecord::Base
  include Auth::TwoFactorAuthentication

  comma do
    id
    title
    first_name
    middle_name
    last_name
    email
    date_of_birth

    address 'Address Number' do |address| address&.address_number end
    address 'Address 1' do |address| address&.address_1 end
    address 'Address 2' do |address| address&.address_2 end
    address 'City' do |address| address&.city end
    address 'Region' do |address| address&.region end
    address 'Post code' do |address| address&.post_code end
    address 'Country' do |address| address&.country end

    phone_number

    country_of_residence
    nationality
    occupation

    employment_status
    experience
    planned_investment

    kind
    certification

    call_me
    marketing
    confirmed_at
    sign_in_count
    last_sign_in_at
    created_at
    updated_at
    aasm_state 'State'

    purchased
    sold

    referer_host
    referer
  end

  def self.search(search, aasm_state = nil, referer_host = nil)
    scope = all
    scope = scope.where(query, "%#{search}%", "%#{search}%") if search.present?
    scope = scope.where(aasm_state: aasm_state) if aasm_state.present?
    scope = scope.where(referer_host: referer_host) if referer_host.present?

    scope
  end

  def self.query
    if ActiveRecord::Base.connection.instance_values['config'][:adapter] == 'mysql2'
      'CONCAT(first_name, " ", last_name) LIKE ? OR email LIKE ?'
    else
      'first_name || " " || last_name LIKE ? OR email LIKE ?'
    end
  end

  def self.ransackable_attributes(auth_object = nil)
    %w(first_name last_name email)
  end

  def self.ransackable_associations(auth_object = nil)
    ["payout_properties", "payout_rents"]
  end

  def legal?
    is_a?(User::Legal)
  end

  def kind
    legal? ? 'Business' : 'Regular'
  end

  def certification
    certification_level&.name&.capitalize
  end

  def current_balance
    @current_balance ||= currency(wallet.balance) rescue 0.00
  end

  def purchased
    @purchased ||= currency(share_logs.where('quantity > 0').sum(:total_amount))
  end

  def sold
    @sold ||= currency(share_logs.where('quantity < 0').sum(:total_amount))
  end

  private

  def currency(amount)
    (amount.to_d / 100).round(2)
  end
end
