require_dependency UownCore::Engine.root.join('app', 'models', 'share', 'order')

class Share::Order < ActiveRecord::Base
  comma do
    id
    user 'Name' do |user| user.display_name end
    user 'Email' do |user| user.email end
    user 'Certification' do |user| user.certification end
    property 'Property' do |property| property.name end
    total_amount_in_pounds 'Total amount'
    type
    quantity
    quantity_remaining
    aasm_state 'State'
    created_at
  end

  def total_amount_in_pounds
    total_amount.to_d / 100
  end

  def cancellable?
    (type == 'Share::SellOrder' && aasm_state == 'active') ||
    (type == 'Share::EasyExitOrder' && aasm_state == 'pending')
  end

  def self.ransackable_attributes(auth_object = nil)
    ["quantity"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["property", "user"]
  end
end
