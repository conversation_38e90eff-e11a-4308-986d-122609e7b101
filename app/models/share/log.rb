require_dependency UownCore::Engine.root.join('app', 'models', 'share', 'log')

class Share::Log < ActiveRecord::Base
  comma do
    id
    user 'Name' do |user| user.display_name end
    user 'Email' do |user| user.email end
    user 'Certification' do |user| user.certification end
    property 'Property' do |property| property.name end
    quantity
    created_at
  end

  comma :portfolio_item do
    user_id 'User ID'
    user 'User Email' do |user| user.email end
    user 'User Name' do |user| user.display_name end
    user 'User Phone' do |user| user.phone_number end
    property 'Property Name' do |property| property.name end
    total 'Total Quantity'
  end
end
