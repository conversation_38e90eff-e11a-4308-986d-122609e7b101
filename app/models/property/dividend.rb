require_dependency UownCore::Engine.root.join('app', 'models', 'property', 'dividend')

class Property::Dividend < ActiveRecord::Base
  comma do
    id
    user 'Name' do |user| user.display_name end
    user 'Email' do |user| user.email end
    user 'Certification' do |user| user.certification end
    property 'Property' do |property| property.name end
    amount_in_pounds 'Amount'
    aasm_state 'State'
    reason
    created_at
  end

  def amount_in_pounds
    amount.to_d / 100
  end
end
