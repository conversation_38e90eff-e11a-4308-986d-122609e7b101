require_dependency UownCore::Engine.root.join('app', 'models', 'mandate')

class Mandate < ActiveRecord::Base
  comma do
    id
    user 'Name' do |user| user.display_name end
    user 'Email' do |user| user.email end
    user 'Certification' do |user| user.certification end
    day
    amount_in_pounds 'Amount'
    bank_account_id 'Mangopay bank account id'
    mangopay_mandate_id 'Mangopay mandate id'
    created_at
    cancelled_at
  end

  def amount_in_pounds
    amount.to_d / 100
  end

  def self.ransackable_attributes(auth_object = nil)
    ["day"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["user"]
  end
end
