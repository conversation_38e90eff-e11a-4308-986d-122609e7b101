module ApplicationHelper
  RECAPTCHA_SITE_KEY = Rails.application.secrets.recaptcha[:site_key]
  include Ransack::Helpers::FormHelper

  def include_recaptcha_js
    raw %Q{
      <script src="https://www.google.com/recaptcha/api.js?render=#{RECAPTCHA_SITE_KEY}"></script>
    }
  end
  def recaptcha_execute(action)
    id = "recaptcha_token_#{SecureRandom.hex(10)}"
    raw %Q{
      <input name="recaptcha_token" type="hidden" id="#{id}"/>
      <script>
        function executeRecaptcha(action, id) {
          grecaptcha.execute('#{RECAPTCHA_SITE_KEY}', { action: action }).then(function (token) {
            document.getElementById(id).value = token;
          });
        }

        grecaptcha.ready(function() {
          executeRecaptcha('#{action}', '#{id}');
        });

        setInterval(function () {
          grecaptcha.ready(function () {
            executeRecaptcha('#{action}', '#{id}');
          });
        }, 100 * 1000);
      </script>
    }
  end
end
