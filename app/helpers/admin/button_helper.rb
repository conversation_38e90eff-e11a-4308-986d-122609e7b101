module Admin::<PERSON><PERSON><PERSON><PERSON><PERSON>
  def edit_button(resource)
    link = if resource.class == String
             resource
           else
             [:edit, :admin, resource]
           end

    link_to "#{icon('edit')} Edit".html_safe, link, class: 'btn btn-primary'
  end

  def delete_button(resource, message = 'Are you sure?')
    link = if resource.class == String
             resource
           else
             [:admin, resource]
           end

    link_to "#{icon('remove')} Delete".html_safe, link, method: :delete,
                                                        remote: true,
                                                        data: { confirm: message },
                                                        class: 'btn btn-danger'
  end
end
