module Admin::LayoutHelper
  def admin_box(title, icon, &block)
    render partial: 'admin/shared/admin_box', locals: { title: title, icon: icon, content: capture(&block) }
  end

  def admin_table(title, icon, &block)
    render partial: 'admin/shared/admin_table', locals: { title: title, icon: icon, content: capture(&block) }
  end

  def admin_form_for(record, options = {}, &proc)
    title = if options[:title].present?
              options[:title]
            else
              %w(new create).include?(params[:action]) ? "Add #{@title.singularize}" : "Edit #{@title.singularize}"
            end

    layout = options[:layout].present? ? options[:layout] : :horizontal

    admin_box title, 'th-list' do
      bootstrap_nested_form_for(record, options.merge(layout: layout), &proc)
    end
  end

  def icon(kind)
    "<i class='glyphicon glyphicon-#{kind}'></i>".html_safe
  end

  def submit_button(form_builder, text)
    render partial: 'admin/shared/submit', locals: { f: form_builder, text: text }
  end
end
