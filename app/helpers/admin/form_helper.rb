module Admin::FormHelper
  def users_for_select_field(excluded_states = %w[registered rejected_age rejected_country rejected_nationality])
    User.where.not(aasm_state: excluded_states)
        .order(:first_name, :last_name, :email)
        .pluck(:id, :first_name, :last_name, :email)
        .collect { |u| ["#{[u[1], u[2]].join(' ')} (#{u[3]})", u[0]] }
  end

  def live_properties
    Property.live.map { |property| [ property.name, property.id] }
  end
end
