module Admin::MangopayLinkHelper
  def mangopay_user_link(user_id, options = {})
    link_to "#{icon('user')} Mangopay User".html_safe, "#{mangopay_host}/User/#{user_id}/Details", options.merge(target: '_blank')
  end

  def mangopay_wallet_link(user_id, wallet_id, options = {})
    # link_to "#{icon('book')} Mangopay Wallet".html_safe, "#{mangopay_host}/User/#{user_id}/Wallets/#{wallet_id}", options.merge(target: '_blank')
  end

  def mangopay_transfer_link(transfer_id, options = {})
    # link_to "#{icon('retweet')} Mangopay Transaction".html_safe, "#{mangopay_host}/Transfer/#{transfer_id}", options.merge(target: '_blank')
  end

  def mangopay_payin_link(payin_id, options = {})
    link_to "#{icon('upload')} Mangopay PayIn".html_safe, "#{mangopay_host}/PayIn/#{payin_id}", options.merge(target: '_blank')
  end

  def mangopay_payout_link(payout_id, options = {})
    link_to "#{icon('download')} Mangopay PayOut".html_safe, "#{mangopay_host}/PayOut/#{payout_id}", options.merge(target: '_blank')
  end

  private

  def mangopay_host
    MangoPay.configuration.preproduction ? 'https://dashboard.sandbox.mangopay.com' : 'https://dashboard.mangopay.com'
  end
end
