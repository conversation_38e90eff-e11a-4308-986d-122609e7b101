# UOWN Admin

# How to use this Readme

This Readme contains two sets of instructions:
* Steps for building and using the application through Docker (recommended for maximum isolation and simplicity), and
* Steps for running the application directly on your host system.

Follow the instructions for each section under the **Using Docker** or **Directly on host system** sub-headings, depending on your preferred choice.

See the [Docker in detail](#Docker-in-detail) section for detailed information of how it works.

# Setting up the repository for development

## Downloading the source

Clone the repo (including submodules):

```bash
<NAME_EMAIL>:dubitplatform/uown-admin.git uown-admin && cd uown-admin
```

Check out the `development` branch:

```bash
git checkout development
```

At this point, you should switch to the `development` branch version of this Readme for the most up-to-date instructions.

## Installing and running the correct runtime

### Using Docker

#### Building the Docker container

The Dockerfile requires the `--ssh default` Docker Buildkit option to allow connecting your host machine's SSH agent to the Docker Daemon for the build process (in order to authenticate with Bitbucket for downloading the private repositories). Because Docker Compose [doesn't currently support this option](https://github.com/docker/compose/issues/7025) you need to build the Docker image using `docker build` separately:

**Note:** You will need to rebuild the image every time you upgrade Ruby or change the gems in the bundle. This means it's likely good practice to build the docker image whenever you switch branches or commits.

```bash
# If your host machine has 4 CPU cores or above, it will be faster to run bundler in parallel:
DOCKER_BUILDKIT=1 docker build -t `source .env && echo $DOCKER_IMAGE` --ssh default --build-arg BUNDLER_JOBS=3 .

# Otherwise, or if you're not sure
DOCKER_BUILDKIT=1 docker build -t `source .env && echo $DOCKER_IMAGE` --ssh default .
```

### Directly on host system

#### Installing the correct runtime version

If you're going to use your host system, it's recommended you [install rvm](https://rvm.io/rvm/install) before proceeding. The version of rvm that you use is not particularly important - you're encouraged to regularly update to get the latest bugfixes and features.

Use `rvm` to install the correct Ruby language version:

```bash
rvm install `cat .ruby-version`
```

#### Installing the correct versions of dependencies

Install the same version of Bundler for that Ruby version, as has previously been used for managing the Gemfile:

```bash
which bundler || gem install bundler -v `sed -n -e '/BUNDLED WITH/,1{n;p;}' Gemfile.lock
```

Use `bundler` to install the application's gems:

```
bundle install
```

## Setup the development and test databases

You can either start from an empty database, or see your teammates for a suitable database dump as a starting point for your development (recommended).

### Using Docker

You'll likely want to start from a recent database dump. To import the sql file (skip this step if starting from a fresh database):

⚠️ This command may fail the first time you run it because it doesn't wait for the database to be up and ready before it attempts to import the database dump. If this happens, wait 30 seconds and try again.

```bash
docker-compose run --rm db-console < database-dump.sql
```

Start the `console` container, run any outstanding migrations:

```bash
docker-compose run --rm console

./bin/rails db:migrate
./bin/rails db:seed
```

To set up the test database, create a separate `test-console` container, to run all migrations and import the database seeds:

```bash
docker-compose run --rm test-console

./bin/rake db:migrate
./bin/rails db:seed
```

### Directly on host system

Create the test and development databases:

```bash
rails db:create
rails db:create RAILS_ENV=test
```

You'll likely want to start from a recent database dump for your development database:

```bash
psql uown_development < database-dump.sql
```

Run all outstanding migrations:

```bash
rails db:migrate
rails db:seed
rails db:migrate RAILS_ENV=test
```

## Starting the Rails server

Once you've built your Docker image locally (and tagged it as `dubit/uown-admin:latest`) you can start the application using Docker Compose:

### Using Docker

```bash
docker-compose up app
```

### Directly on host system

```bash
rails s
```

## Using the rails console

It may be necessary for you to access the rails console to perform certain actions.  You can do this as below:

### Using Docker

To open up a new rails console run:

```bash
docker-compose run --rm rails-console
```

### Directly on host system

Open the rails console with:

```bash
rails c
```

## Running the test suite

### Using Docker

You can run the tests after you've built the Docker image:

```bash
docker-compose run --rm tests
```

To run only the tests that failed on the most recent run:

```bash
docker-compose run --rm tests-only-failures
```

### Directly on host system

The entire test suite can be run directly with RSpec:

```bash
bundle exec rspec
```

If you do so and tests fail, the results are saved in a temporary file so the next time you run the tests, you have the option of only running those that failed on the previous execution:

```bash
bundle exec rspec --only-failures
```

## Adding gems to the bundle or modifying the Gemfile

To add gems to the application's bundle, you need to list them in the `Gemfile`.

### Using Docker

Stop any running service:

```bash
docker-compose down
```

Run the console services and generate the new `Gemfile.lock`:

```bash
docker-compose run --rm console
./bin/bundle install
```

Then rebuild the Docker image, to include the new gems (see build command above). You can now run your services with the new gem available.

### Directly on host system

Install the bundle directly on your host system:

```bash
bundle install
```

# Overview

## JSON API

This project uses Netflix's implementation of the [JSON API specification](https://jsonapi.org/): [jsonapi-serializer](https://github.com/jsonapi-serializer/jsonapi-serializer/) to provide JSON serializers.

To define a new serializer, add a file in the `app/serializers` directory and have the class inherit from `ApplicationSerializer`.

```ruby
module API
  module V1
    class UserSerializer < ApplicationSerializer

    end
   end
end
```

The project uses [json_parameters](https://github.com/visualitypl/jsonapi_parameters) to parse JSON request parameters to the format that Rails expects. You need to use the `from_jsonapi` helper method:

```ruby
def create_params
  params.from_jsonapi.require(:model).permit(:name)
end
```

# Error reporting

This project uses the [airbrake gem](https://github.com/airbrake/airbrake) to report errors to an Errbit server. Configure your project's details in `config/initializers/airbrake_as_errbit.rb`.

Uncaught exceptions are reported by default, but if you want to explicitly report an error, use the [Airbrake.notify method](https://github.com/airbrake/airbrake-ruby#airbrakenotify).

Use the `ERROR_ENVIRONMENT` environment variable to set what environment is reported to Errbit (otherwise the current Rails environment is used).

# Docker in detail

## Configuration files

The `Dockerfile` contains the instructions for building the Docker image used in production and in development.

The Docker image contains all the dependencies for the rails application, isolated from your host system so there is no contamination between applications.

The `docker-compose.yml` file contains the additional configuration that make it easier to use the Docker image for development. In production, the Docker image is instead used with production credentials and settings, and will link to services that are suitable for production use.

## Rails environments and service types

The `docker-compose.yml` file contains configuration for both the development and test Rails environments, to provide a single interface when interacting with the application.

There are two types of services across these two environments:

* Ones you want to run as daemons which will take no user input - you run these with `docker-compose up <service-name>`
* And ones you want to interact with - run these with `docker-compose run --rm <service-name>`.

## Types of consoles

The main entrypoint for each environment are the `console` and `test-console` services - these create bash sessions for the different environments, allowing you to run commands like `./bin/rails db:migrate` and `./bin/bundle install`. From here you can also open up rails consoles with `./bin/rails c`. There are, however, separate services that provide direct access to the different consoles you may want:

* `docker-compose run --rm console`
* `docker-compose run --rm db-console`
* `docker-compose run --rm rails-console`

## Test suite

To run the test suite, you can run the `test-console` and run `rspec`, however there are also convenience services for running the test suite in common configurations. See the `docker-compose.yml` file for details.

## Service links

A number of services depend on others being available in order for them to function correctly. This is declared in the `docker-compose.yml` file using `links:`. When starting a service that depends on another, the other service will also be started. The service, however, will not wait for its dependencies to be _ready_ before it first attempts to make a connection with them, so we use the `wait-for-it.sh` script to wait for a service to be listening on a particular port before running a subsequent command.

## Volumes and the read-write layer of containers

When a container reads or writes from the file system, it is doing so in one of 3 different places.

* Container's read/write layer: This is the default and should usually be treated as read-only. Writing files back to the container's read/write layer grow's the containers size and is slower than volumes. Particularly for services that write to the file system regularly (e.g. log files), volumes should be used instead.
* Named volumes: These are defined with the `volumes` option in the `docker-compose.yml` file and mount volumes in the service container(s), for more efficient I/O and allowing different services to share the same volume. This is how, for example, the `tests` service generates test files to the `tmp` directory to have them available for the `chrome` service to upload as part of the Selenium tests.
* Bind mounts: These mount directories from the host system into the containers. In recent versions of Docker for Mac, you can specify the type of bind mount and declare whether it should be the host (`:cached`) or the container (`:delegated`) that has authority over the directory. This is how you can change files in your IDE on your host system and have them applied to the `app` service immediately.

## When to rebuild the Docker image

Whenever you add a new dependency (typically a `apt` package) to the Docker image, you'll need to rebuild the `Dockerfile` (see instructions above).

If you add a new _private_ gem to the `Gemfile` (requiring access to the SSH agent running on your host machine), you'll also need to rebuild the Docker image.

## Stopping background services

Any service you run with `docker-compose up <service-name>` will (by default) stop when you press Ctrl+C - but any _dependent_ services will continue to run in the background and may cause port conflicts when running other applications.

To stop all services defined in `docker-compose.yml`:

```bash
docker-compose down
```
